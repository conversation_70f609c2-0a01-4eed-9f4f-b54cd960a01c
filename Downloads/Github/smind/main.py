#!/usr/bin/env python3
"""
SEO自动化检查工具
作者: 测试工程师
功能: 对网站进行全面的SEO检查并生成HTML报告
"""

import sys
import argparse
import validators
from seo_checker import SEOChecker
from report_generator import ReportGenerator

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SEO自动化检查工具')
    parser.add_argument('url', help='要检查的网站URL')
    parser.add_argument('-o', '--output', default='seo_report.html', 
                       help='输出报告文件名 (默认: seo_report.html)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='显示详细输出')
    
    args = parser.parse_args()
    
    # 验证URL
    if not validators.url(args.url):
        print(f"错误: 无效的URL格式: {args.url}")
        sys.exit(1)
    
    print("=" * 60)
    print("🔍 SEO自动化检查工具")
    print("=" * 60)
    print(f"检查网站: {args.url}")
    print(f"输出文件: {args.output}")
    print("-" * 60)
    
    try:
        # 创建SEO检查器
        checker = SEOChecker(args.url)
        
        # 执行检查
        print("正在执行SEO检查...")
        results = checker.check_all()
        
        if results is None:
            print("❌ 检查失败: 无法获取网站内容")
            sys.exit(1)
        
        # 显示简要结果
        if 'total_score' in results:
            score = results['total_score']['score']
            grade = results['total_score']['grade']
            print(f"✅ 检查完成!")
            print(f"📊 总体评分: {score} ({grade})")
        
        # 生成报告
        print("正在生成HTML报告...")
        generator = ReportGenerator()
        output_file = generator.generate_html_report(results, args.url, args.output)
        
        print("=" * 60)
        print("🎉 SEO检查完成!")
        print(f"📄 报告已保存到: {output_file}")
        print("💡 请在浏览器中打开报告文件查看详细结果")
        print("=" * 60)
        
        # 显示详细结果（如果启用verbose模式）
        if args.verbose:
            print_detailed_results(results)
            
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

def print_detailed_results(results):
    """打印详细结果"""
    print("\n📋 详细检查结果:")
    print("-" * 40)
    
    # 基础SEO
    if 'basic_seo' in results:
        print("\n🔍 基础SEO:")
        basic = results['basic_seo']
        
        if 'title' in basic:
            title_data = basic['title']
            print(f"  标题: {title_data['score']}/100")
            print(f"    内容: {title_data['content'][:50]}...")
            print(f"    长度: {title_data['length']} 字符")
        
        if 'meta_description' in basic:
            desc_data = basic['meta_description']
            print(f"  描述: {desc_data['score']}/100")
            print(f"    长度: {desc_data['length']} 字符")
        
        if 'headings' in basic and 'h1' in basic['headings']:
            h1_data = basic['headings']['h1']
            print(f"  H1标签: {h1_data['score']}/100")
            print(f"    数量: {h1_data['count']}")
        
        if 'images' in basic:
            img_data = basic['images']
            print(f"  图片: {img_data['score']}/100")
            print(f"    总数: {img_data['total']}, 缺少alt: {img_data['without_alt']}")
    
    # 技术SEO
    if 'technical_seo' in results:
        print("\n⚙️ 技术SEO:")
        tech = results['technical_seo']
        
        if 'robots_txt' in tech:
            robots_data = tech['robots_txt']
            print(f"  Robots.txt: {robots_data['score']}/100")
            print(f"    存在: {'是' if robots_data['exists'] else '否'}")
        
        if 'sitemap' in tech:
            sitemap_data = tech['sitemap']
            print(f"  网站地图: {sitemap_data['score']}/100")
            print(f"    存在: {'是' if sitemap_data['exists'] else '否'}")
        
        if 'internal_links' in tech:
            links_data = tech['internal_links']
            print(f"  内部链接: {links_data['score']}/100")
            print(f"    数量: {links_data['count']}")
    
    # 性能
    if 'performance' in results:
        print("\n🚀 性能:")
        perf = results['performance']
        
        if 'load_time' in perf:
            load_data = perf['load_time']
            print(f"  加载时间: {load_data['score']}/100")
            print(f"    时间: {load_data['time']} 秒")
        
        if 'page_size' in perf:
            size_data = perf['page_size']
            print(f"  页面大小: {size_data['score']}/100")
            print(f"    大小: {size_data['size']} KB")
    
    # 移动端
    if 'mobile' in results:
        print("\n📱 移动端:")
        mobile = results['mobile']
        
        if 'viewport' in mobile:
            viewport_data = mobile['viewport']
            print(f"  Viewport: {viewport_data['score']}/100")
            print(f"    存在: {'是' if viewport_data['exists'] else '否'}")
    
    # 安全
    if 'security' in results:
        print("\n🔒 安全:")
        security = results['security']
        
        if 'https' in security:
            https_data = security['https']
            print(f"  HTTPS: {https_data['score']}/100")
            print(f"    启用: {'是' if https_data['enabled'] else '否'}")
    
    # 内容分析
    if 'content_analysis' in results:
        print("\n📝 内容分析:")
        content = results['content_analysis']
        
        if 'word_count' in content:
            word_data = content['word_count']
            print(f"  内容长度: {word_data['score']}/100")
            print(f"    词数: {word_data['count']}")

if __name__ == "__main__":
    main()
