
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://github.com</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://github.com</div>
            <div class="date">生成时间: 2025-05-26 09:37:36</div>
        </div>

        <!-- 问题总结概览 -->
        
        <div class="issues-overview">
            <div class="status-icon">🔧</div>
            <div class="status-message">发现1个重要问题，建议处理</div>
            <p>共发现 5 个问题和建议</p>

            
            <div class="issues-stats">
                

                
                <div class="issue-stat">
                    <div class="issue-count important-count">1</div>
                    <div>重要问题</div>
                </div>
                

                
                <div class="issue-stat">
                    <div class="issue-count minor-count">2</div>
                    <div>轻微问题</div>
                </div>
                

                
                <div class="issue-stat">
                    <div class="issue-count suggestions-count">2</div>
                    <div>优化建议</div>
                </div>
                
            </div>
            
        </div>
        

        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>📝 页面标题</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> GitHub · Build and ship software on a single, collaborative platform · GitHub<br>
                        <strong>长度:</strong> 77 字符
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>标题太长，建议不超过60个字符</li>
                            
                        </ul>
                    </div>
                    
                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item no-issues">
                    <div class="check-name">
                        <span>📄 Meta描述</span>
                        
                        <span class="status-badge status-good">✓ 正常</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> GitHub is where people build software. More than 150 million people use GitHub to discover, fork, and contribute to over 420 million projects.<br>
                        <strong>长度:</strong> 142 字符
                    </div>
                    
                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>🏷️ 标题结构</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        
                        <strong>H1:</strong> 4 个
                        
                        <br>&nbsp;&nbsp;内容: Search code, repositories, users, issues, pull requests..., Provide feedback...
                        
                        <br>
                        
                        <strong>H2:</strong> 11 个
                        
                        <br>&nbsp;&nbsp;内容: Navigation Menu, Use saved searches to filter your results more quickly...
                        
                        <br>
                        
                        <strong>H3:</strong> 16 个
                        
                        <br>&nbsp;&nbsp;内容: Work 55% faster.Jump to footnote 1 Increase productivity with AI-powered coding assistance, including code completion, chat, and more., Automate any workflow...
                        
                        <br>
                        
                        <strong>H4:</strong> 0 个
                        
                        <br>
                        
                        <strong>H5:</strong> 0 个
                        
                        <br>
                        
                        <strong>H6:</strong> 0 个
                        
                        <br>
                        
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>每个页面应该只有一个H1标签</li>
                            
                        </ul>
                    </div>
                    
                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>🖼️ 图片优化</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>总图片数:</strong> 54<br>
                        <strong>缺少alt属性:</strong> 14
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>14张图片缺少alt属性</li>
                            
                        </ul>
                    </div>
                    
                </div>
                
            </div>
        </div>
        

        <!-- 问题详细列表 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 问题详细分析</h2>
            </div>
            <div class="section-content">
                <!-- 严重问题 -->
                

                <!-- 重要问题 -->
                
                <div class="issue-category important-issues">
                    <h3>⚠️ 重要问题 (1个)</h3>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - 图片优化: 14张图片缺少alt属性</div>
                        <div class="issue-impact">影响: 影响可访问性和图片搜索排名</div>
                        <div class="issue-solution">解决方案: 为所有图片添加描述性的alt属性</div>
                    </div>
                    
                </div>
                

                <!-- 轻微问题 -->
                
                <div class="issue-category minor-issues">
                    <h3>🔧 轻微问题 (2个)</h3>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - 页面标题: 标题过长（77字符）</div>
                        <div class="issue-impact">影响: 搜索结果中可能被截断</div>
                        <div class="issue-solution">解决方案: 建议标题长度不超过60字符</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - H1标签: 页面有4个H1标签</div>
                        <div class="issue-impact">影响: 可能分散关键词权重</div>
                        <div class="issue-solution">解决方案: 每个页面应该只有一个H1标签</div>
                    </div>
                    
                </div>
                

                <!-- 优化建议 -->
                
                <div class="issue-category suggestions">
                    <h3>💡 优化建议 (2个)</h3>
                    
                    <div class="issue-item">
                        <div class="issue-header">技术SEO - XML网站地图: 网站缺少XML网站地图</div>
                        <div class="issue-impact">影响: 搜索引擎难以发现所有页面</div>
                        <div class="issue-solution">解决方案: 创建并提交XML网站地图到搜索引擎</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">移动端优化 - 响应式设计: 未检测到响应式设计</div>
                        <div class="issue-impact">影响: 可能影响移动端用户体验</div>
                        <div class="issue-solution">解决方案: 考虑实现响应式设计以适配不同设备</div>
                    </div>
                    
                </div>
                
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-26 09:37:36</p>
        </div>
    </div>
</body>
</html>
        