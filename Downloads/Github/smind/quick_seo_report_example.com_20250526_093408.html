
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://example.com</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://example.com</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: 2025-05-26 09:34:08
            </div>
        </div>

        <!-- 问题总结概览 -->
        
        <div class="issues-overview">
            <div class="status-icon">⚠️</div>
            <h2>发现4个重要问题，建议优先处理</h2>
            <p>共发现 6 个问题和建议</p>

            <div class="issues-stats">
                

                
                <div class="issue-stat">
                    <div class="issue-count important-count">4</div>
                    <div>重要问题</div>
                </div>
                

                

                
                <div class="issue-stat">
                    <div class="issue-count suggestions-count">2</div>
                    <div>优化建议</div>
                </div>
                
            </div>
        </div>
        

        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面标题</strong>
                        <div class="details">
                            内容: Example Domain<br>
                            长度: 14 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>标题太短，建议至少30个字符</li>
                                
                            </ul>
                        </div>
                        
                    </div>

                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Meta描述</strong>
                        <div class="details">
                            内容: 无<br>
                            长度: 0 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>页面缺少meta描述</li>
                                
                            </ul>
                        </div>
                        
                    </div>

                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            
                            H1: 1 个
                            
                            <br>内容: Example Domain
                            
                            <br>
                            
                            H2: 0 个
                            
                            <br>
                            
                            H3: 0 个
                            
                            <br>
                            
                            H4: 0 个
                            
                            <br>
                            
                            H5: 0 个
                            
                            <br>
                            
                            H6: 0 个
                            
                            <br>
                            
                        </div>
                        
                    </div>

                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: 0<br>
                            缺少alt属性: 0
                        </div>
                        
                    </div>

                </div>
                
            </div>
        </div>
        

        <!-- 技术SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: 否
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加robots.txt文件</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Sitemap检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: 否
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加XML网站地图</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                        
                    </div>
                </div>
                

                <!-- 内部链接检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: 0
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>增加内部链接以改善网站结构</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 性能检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: 0.76 秒
                        </div>
                        
                    </div>
                        
                    </div>
                </div>
                

                <!-- 页面大小检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: 1.23 KB
                        </div>
                        
                    </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 移动端友好性 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: 是
                            
                            <br>内容: width=device-width, initial-scale=1
                            
                        </div>
                        
                    </div>
                        
                    </div>
                </div>
                

                <!-- 响应式设计检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: 是
                        </div>
                        
                    </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 安全检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: 是
                        </div>
                        
                    </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 内容分析 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: 30
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>内容太少，建议至少300个词</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 问题详细列表 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 问题详细分析</h2>
            </div>
            <div class="section-content">
                <div class="issue-list">
                    <!-- 严重问题 -->
                    

                    <!-- 重要问题 -->
                    
                    <div class="issue-category important-issues">
                        <h3>⚠️ 重要问题 (4个)</h3>
                        
                        <div class="issue-item">
                            <div class="issue-header">基础SEO - 页面标题: 标题过短（14字符）</div>
                            <div class="issue-impact">影响: 可能无法充分描述页面内容</div>
                            <div class="issue-solution">解决方案: 建议标题长度至少30字符</div>
                        </div>
                        
                        <div class="issue-item">
                            <div class="issue-header">基础SEO - Meta描述: 页面缺少Meta描述</div>
                            <div class="issue-impact">影响: 搜索引擎无法获取页面摘要信息</div>
                            <div class="issue-solution">解决方案: 添加120-160字符的页面描述</div>
                        </div>
                        
                        <div class="issue-item">
                            <div class="issue-header">技术SEO - 内部链接: 内部链接过少（0个）</div>
                            <div class="issue-impact">影响: 影响网站结构和页面权重传递</div>
                            <div class="issue-solution">解决方案: 增加相关页面的内部链接</div>
                        </div>
                        
                        <div class="issue-item">
                            <div class="issue-header">内容质量 - 内容长度: 内容过少（30词）</div>
                            <div class="issue-impact">影响: 可能被搜索引擎认为内容质量低</div>
                            <div class="issue-solution">解决方案: 增加有价值的内容，建议至少300词</div>
                        </div>
                        
                    </div>
                    

                    <!-- 轻微问题 -->
                    

                    <!-- 优化建议 -->
                    
                    <div class="issue-category suggestions">
                        <h3>💡 优化建议 (2个)</h3>
                        
                        <div class="issue-item">
                            <div class="issue-header">技术SEO - Robots.txt: 网站缺少robots.txt文件</div>
                            <div class="issue-impact">影响: 无法指导搜索引擎爬虫行为</div>
                            <div class="issue-solution">解决方案: 创建robots.txt文件指导搜索引擎抓取</div>
                        </div>
                        
                        <div class="issue-item">
                            <div class="issue-header">技术SEO - XML网站地图: 网站缺少XML网站地图</div>
                            <div class="issue-impact">影响: 搜索引擎难以发现所有页面</div>
                            <div class="issue-solution">解决方案: 创建并提交XML网站地图到搜索引擎</div>
                        </div>
                        
                    </div>
                    
                </div>
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-26 09:34:08</p>
        </div>
    </div>
</body>
</html>
        