import os
from datetime import datetime
from jinja2 import Template
import config

class ReportGenerator:
    def __init__(self):
        self.template_dir = 'templates'
        self.static_dir = 'static'

    def generate_html_report(self, seo_results, url, output_file='seo_report.html'):
        """生成HTML报告"""

        # 确保目录存在
        os.makedirs(self.template_dir, exist_ok=True)
        os.makedirs(self.static_dir, exist_ok=True)

        # 创建CSS样式文件
        self._create_css_file()

        # 创建HTML模板
        template_content = self._get_html_template()
        template = Template(template_content)

        # 准备模板数据
        template_data = {
            'url': url,
            'results': seo_results,
            'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'config': config.REPORT_CONFIG
        }

        # 渲染HTML
        html_content = template.render(**template_data)

        # 保存文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"报告已生成: {output_file}")
        return output_file

    def _create_css_file(self):
        """创建CSS样式文件"""
        css_content = """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .url {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .issues-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .status-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .issues-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .issue-stat {
            text-align: center;
            margin: 10px;
        }

        .issue-count {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .critical-count { color: #F44336; }
        .important-count { color: #FF9800; }
        .minor-count { color: #FFC107; }
        .suggestions-count { color: #2196F3; }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }

        .section-content {
            padding: 20px;
        }

        .check-item {
            background: #f8f9fa;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #dee2e6;
        }

        .check-item.has-issues {
            border-left-color: #ffc107;
            background: #fff8e1;
        }

        .check-item.no-issues {
            border-left-color: #28a745;
            background: #f8fff9;
        }

        .check-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }

        .check-details {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border: 1px solid #e9ecef;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-good {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .issue-list {
            margin-top: 30px;
        }

        .issue-category {
            margin-bottom: 25px;
        }

        .issue-category h3 {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
        }

        .critical-issues h3 { background: #F44336; }
        .important-issues h3 { background: #FF9800; }
        .minor-issues h3 { background: #FFC107; color: #333; }
        .suggestions h3 { background: #2196F3; }

        .issue-item {
            background: #f8f9fa;
            border-left: 4px solid #dee2e6;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 5px 5px 0;
        }

        .critical-issues .issue-item { border-left-color: #F44336; }
        .important-issues .issue-item { border-left-color: #FF9800; }
        .minor-issues .issue-item { border-left-color: #FFC107; }
        .suggestions .issue-item { border-left-color: #2196F3; }

        .issue-header {
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
        }

        .issue-impact {
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 8px;
        }

        .issue-solution {
            color: #28a745;
            font-weight: 500;
        }

        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }

        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendations li:before {
            content: "⚠";
            position: absolute;
            left: 0;
            color: #856404;
        }

        .details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .score-circle {
                width: 120px;
                height: 120px;
                font-size: 2em;
            }

            .check-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .check-score {
                margin-left: 0;
                margin-top: 10px;
            }
        }
        """

        css_file = os.path.join(self.static_dir, 'style.css')
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(css_content)

    def _get_html_template(self):
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config.title }} - {{ url }}</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>{{ config.title }}</h1>
            <div class="url">{{ url }}</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: {{ report_date }}
            </div>
        </div>

        <!-- 问题总结概览 -->
        {% if results.issues_summary %}
        <div class="issues-overview">
            <div class="status-icon">{{ results.issues_summary.status.icon }}</div>
            <h2>{{ results.issues_summary.status.message }}</h2>
            <p>共发现 {{ results.issues_summary.total_issues }} 个问题和建议</p>

            <div class="issues-stats">
                {% if results.issues_summary.critical_count > 0 %}
                <div class="issue-stat">
                    <div class="issue-count critical-count">{{ results.issues_summary.critical_count }}</div>
                    <div>严重问题</div>
                </div>
                {% endif %}

                {% if results.issues_summary.important_count > 0 %}
                <div class="issue-stat">
                    <div class="issue-count important-count">{{ results.issues_summary.important_count }}</div>
                    <div>重要问题</div>
                </div>
                {% endif %}

                {% if results.issues_summary.minor_count > 0 %}
                <div class="issue-stat">
                    <div class="issue-count minor-count">{{ results.issues_summary.minor_count }}</div>
                    <div>轻微问题</div>
                </div>
                {% endif %}

                {% if results.issues_summary.suggestions_count > 0 %}
                <div class="issue-stat">
                    <div class="issue-count suggestions-count">{{ results.issues_summary.suggestions_count }}</div>
                    <div>优化建议</div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 基础SEO -->
        {% if results.basic_seo %}
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                {% if results.basic_seo.title %}
                <div class="check-item {{ 'has-issues' if results.basic_seo.title.recommendations else 'no-issues' }}">
                    <div class="check-name">
                        📝 页面标题
                        {% if not results.basic_seo.title.recommendations %}
                        <span class="status-badge status-good">✓ 正常</span>
                        {% else %}
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        {% endif %}
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> {{ results.basic_seo.title.content or '无' }}<br>
                        <strong>长度:</strong> {{ results.basic_seo.title.length }} 字符
                    </div>
                    {% if results.basic_seo.title.recommendations %}
                    <div class="recommendations">
                        <h4>💡 优化建议:</h4>
                        <ul>
                            {% for rec in results.basic_seo.title.recommendations %}
                            <li>{{ rec }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- Meta描述检查 -->
                {% if results.basic_seo.meta_description %}
                <div class="check-item {{ 'has-issues' if results.basic_seo.meta_description.recommendations else 'no-issues' }}">
                    <div class="check-name">
                        📄 Meta描述
                        {% if not results.basic_seo.meta_description.recommendations %}
                        <span class="status-badge status-good">✓ 正常</span>
                        {% else %}
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        {% endif %}
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> {{ results.basic_seo.meta_description.content or '无' }}<br>
                        <strong>长度:</strong> {{ results.basic_seo.meta_description.length }} 字符
                    </div>
                    {% if results.basic_seo.meta_description.recommendations %}
                    <div class="recommendations">
                        <h4>💡 优化建议:</h4>
                        <ul>
                            {% for rec in results.basic_seo.meta_description.recommendations %}
                            <li>{{ rec }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- 标题结构检查 -->
                {% if results.basic_seo.headings %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            {% for heading, data in results.basic_seo.headings.items() %}
                            {{ heading.upper() }}: {{ data.count }} 个
                            {% if data.content %}
                            <br>内容: {{ data.content[:3]|join(', ') }}{% if data.content|length > 3 %}...{% endif %}
                            {% endif %}
                            <br>
                            {% endfor %}
                        </div>
                        {% if results.basic_seo.headings.h1.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.headings.h1.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>

                </div>
                {% endif %}

                <!-- 图片检查 -->
                {% if results.basic_seo.images %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: {{ results.basic_seo.images.total }}<br>
                            缺少alt属性: {{ results.basic_seo.images.without_alt }}
                        </div>
                        {% if results.basic_seo.images.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.images.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>

                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 技术SEO -->
        {% if results.technical_seo %}
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                {% if results.technical_seo.robots_txt %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: {{ '是' if results.technical_seo.robots_txt.exists else '否' }}
                        </div>
                        {% if results.technical_seo.robots_txt.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.robots_txt.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Sitemap检查 -->
                {% if results.technical_seo.sitemap %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: {{ '是' if results.technical_seo.sitemap.exists else '否' }}
                            {% if results.technical_seo.sitemap.url %}
                            <br>URL: {{ results.technical_seo.sitemap.url }}
                            {% endif %}
                        </div>
                        {% if results.technical_seo.sitemap.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.sitemap.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                        {{ results.technical_seo.sitemap.score }}
                    </div>
                </div>
                {% endif %}

                <!-- 内部链接检查 -->
                {% if results.technical_seo.internal_links %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: {{ results.technical_seo.internal_links.count }}
                        </div>
                        {% if results.technical_seo.internal_links.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.internal_links.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                        {{ results.technical_seo.internal_links.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 性能检查 -->
        {% if results.performance %}
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                {% if results.performance.load_time %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: {{ results.performance.load_time.time }} 秒
                        </div>
                        {% if results.performance.load_time.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.performance.load_time.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                        {{ results.performance.load_time.score }}
                    </div>
                </div>
                {% endif %}

                <!-- 页面大小检查 -->
                {% if results.performance.page_size %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: {{ results.performance.page_size.size }} KB
                        </div>
                        {% if results.performance.page_size.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.performance.page_size.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                        {{ results.performance.page_size.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 移动端友好性 -->
        {% if results.mobile %}
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                {% if results.mobile.viewport %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: {{ '是' if results.mobile.viewport.exists else '否' }}
                            {% if results.mobile.viewport.content %}
                            <br>内容: {{ results.mobile.viewport.content }}
                            {% endif %}
                        </div>
                        {% if results.mobile.viewport.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.mobile.viewport.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                        {{ results.mobile.viewport.score }}
                    </div>
                </div>
                {% endif %}

                <!-- 响应式设计检查 -->
                {% if results.mobile.responsive %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: {{ '是' if results.mobile.responsive.detected else '否' }}
                        </div>
                        {% if results.mobile.responsive.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.mobile.responsive.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                        {{ results.mobile.responsive.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 安全检查 -->
        {% if results.security %}
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                {% if results.security.https %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: {{ '是' if results.security.https.enabled else '否' }}
                        </div>
                        {% if results.security.https.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.security.https.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                        {{ results.security.https.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 内容分析 -->
        {% if results.content_analysis %}
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                {% if results.content_analysis.word_count %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: {{ results.content_analysis.word_count.count }}
                        </div>
                        {% if results.content_analysis.word_count.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.content_analysis.word_count.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                        {{ results.content_analysis.word_count.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 问题详细列表 -->
        {% if results.issues_summary and results.issues_summary.total_issues > 0 %}
        <div class="section">
            <div class="section-header">
                <h2>🔍 问题详细分析</h2>
            </div>
            <div class="section-content">
                <div class="issue-list">
                    <!-- 严重问题 -->
                    {% if results.issues_summary.issues.critical %}
                    <div class="issue-category critical-issues">
                        <h3>🚨 严重问题 ({{ results.issues_summary.critical_count }}个)</h3>
                        {% for issue in results.issues_summary.issues.critical %}
                        <div class="issue-item">
                            <div class="issue-header">{{ issue.type }} - {{ issue.item }}: {{ issue.issue }}</div>
                            <div class="issue-impact">影响: {{ issue.impact }}</div>
                            <div class="issue-solution">解决方案: {{ issue.solution }}</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- 重要问题 -->
                    {% if results.issues_summary.issues.important %}
                    <div class="issue-category important-issues">
                        <h3>⚠️ 重要问题 ({{ results.issues_summary.important_count }}个)</h3>
                        {% for issue in results.issues_summary.issues.important %}
                        <div class="issue-item">
                            <div class="issue-header">{{ issue.type }} - {{ issue.item }}: {{ issue.issue }}</div>
                            <div class="issue-impact">影响: {{ issue.impact }}</div>
                            <div class="issue-solution">解决方案: {{ issue.solution }}</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- 轻微问题 -->
                    {% if results.issues_summary.issues.minor %}
                    <div class="issue-category minor-issues">
                        <h3>🔧 轻微问题 ({{ results.issues_summary.minor_count }}个)</h3>
                        {% for issue in results.issues_summary.issues.minor %}
                        <div class="issue-item">
                            <div class="issue-header">{{ issue.type }} - {{ issue.item }}: {{ issue.issue }}</div>
                            <div class="issue-impact">影响: {{ issue.impact }}</div>
                            <div class="issue-solution">解决方案: {{ issue.solution }}</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- 优化建议 -->
                    {% if results.issues_summary.issues.suggestions %}
                    <div class="issue-category suggestions">
                        <h3>💡 优化建议 ({{ results.issues_summary.suggestions_count }}个)</h3>
                        {% for issue in results.issues_summary.issues.suggestions %}
                        <div class="issue-item">
                            <div class="issue-header">{{ issue.type }} - {{ issue.item }}: {{ issue.issue }}</div>
                            <div class="issue-impact">影响: {{ issue.impact }}</div>
                            <div class="issue-solution">解决方案: {{ issue.solution }}</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 {{ config.company }} SEO审计工具生成</p>
            <p>生成时间: {{ report_date }}</p>
        </div>
    </div>
</body>
</html>
        """
