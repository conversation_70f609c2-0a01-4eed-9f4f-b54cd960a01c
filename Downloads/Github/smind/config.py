# SEO检查工具配置文件

# 用户代理字符串
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# 请求超时时间（秒）
REQUEST_TIMEOUT = 30

# SEO评分权重配置
SCORE_WEIGHTS = {
    'title': 15,           # 页面标题
    'meta_description': 10, # 元描述
    'headings': 10,        # 标题结构
    'images': 10,          # 图片优化
    'links': 10,           # 链接分析
    'page_speed': 15,      # 页面速度
    'mobile_friendly': 10, # 移动端友好
    'ssl': 5,              # SSL证书
    'content_length': 5,   # 内容长度
    'meta_keywords': 5,    # 关键词
    'robots_txt': 5        # robots.txt
}

# 内容长度标准
MIN_CONTENT_LENGTH = 300
OPTIMAL_CONTENT_LENGTH = 1000

# 标题长度标准
MIN_TITLE_LENGTH = 30
MAX_TITLE_LENGTH = 60

# 描述长度标准
MIN_DESCRIPTION_LENGTH = 120
MAX_DESCRIPTION_LENGTH = 160

# 检查项目配置
CHECK_ITEMS = {
    'basic_seo': True,      # 基础SEO检查
    'technical_seo': True,  # 技术SEO检查
    'content_analysis': True, # 内容分析
    'performance': True,    # 性能检查
    'mobile': True,         # 移动端检查
    'security': True        # 安全检查
}

# 报告配置
REPORT_CONFIG = {
    'title': 'SEO审计报告',
    'company': '测试工程师团队',  # 您可以修改为您的公司名称
    'logo_url': '',  # 可选：公司logo URL
    'include_recommendations': True,
    'include_technical_details': True
}
