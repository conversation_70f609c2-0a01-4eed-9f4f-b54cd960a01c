import os
from datetime import datetime
from jinja2 import Template
import config

class ReportGenerator:
    def __init__(self):
        self.template_dir = 'templates'
        self.static_dir = 'static'

    def generate_html_report(self, seo_results, url, output_file='seo_report.html'):
        """生成HTML报告"""

        # 确保目录存在
        os.makedirs(self.template_dir, exist_ok=True)
        os.makedirs(self.static_dir, exist_ok=True)

        # 创建CSS样式文件
        self._create_css_file()

        # 创建HTML模板
        template_content = self._get_html_template()
        template = Template(template_content)

        # 准备模板数据
        template_data = {
            'url': url,
            'results': seo_results,
            'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'config': config.REPORT_CONFIG
        }

        # 渲染HTML
        html_content = template.render(**template_data)

        # 保存文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"报告已生成: {output_file}")
        return output_file

    def _create_css_file(self):
        """创建CSS样式文件"""
        css_content = """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .url {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .header .date {
            opacity: 0.8;
            font-size: 0.9em;
        }

        .issues-overview {
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .status-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .status-message {
            font-size: 1.5em;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }

        .issues-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .issue-stat {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .issue-count {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .critical-count { color: #dc3545; }
        .important-count { color: #fd7e14; }
        .minor-count { color: #ffc107; }
        .suggestions-count { color: #17a2b8; }

        .section {
            margin: 30px;
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            border-left: 4px solid #007bff;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.3em;
            font-weight: 600;
        }

        .section-content {
            background: white;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            border: 1px solid #e9ecef;
            border-top: none;
        }

        .check-item {
            background: #f8f9fa;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .check-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .check-item.has-issues {
            border-left-color: #ffc107;
            background: #fff8e1;
        }

        .check-item.no-issues {
            border-left-color: #28a745;
            background: #f8fff9;
        }

        .check-item.critical {
            border-left-color: #dc3545;
            background: #fdf2f2;
        }

        .check-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .check-details {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 0.9em;
            border: 1px solid #e9ecef;
            line-height: 1.8;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-good {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .recommendations {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .recommendations h4 {
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .recommendations ul {
            list-style: none;
            padding: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
            color: #1565c0;
        }

        .recommendations li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }

        .issue-category {
            margin-bottom: 30px;
        }

        .issue-category h3 {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
        }

        .critical-issues h3 { background: #dc3545; }
        .important-issues h3 { background: #fd7e14; }
        .minor-issues h3 { background: #ffc107; color: #333; }
        .suggestions h3 { background: #17a2b8; }

        .issue-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .issue-header {
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .issue-impact {
            color: #6c757d;
            margin-bottom: 10px;
            font-style: italic;
        }

        .issue-solution {
            color: #28a745;
            font-weight: 500;
            padding: 10px;
            background: #f8fff9;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                border-radius: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .issues-overview {
                padding: 30px 20px;
            }

            .issues-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .section {
                margin: 20px 15px;
            }

            .check-name {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        """

        css_file = os.path.join(self.static_dir, 'style.css')
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(css_content)

    def _get_html_template(self):
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config.title }} - {{ url }}</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>{{ config.title }}</h1>
            <div class="url">{{ url }}</div>
            <div class="date">生成时间: {{ report_date }}</div>
        </div>

        <!-- 问题总结概览 -->
        {% if results.issues_summary %}
        <div class="issues-overview">
            <div class="status-icon">{{ results.issues_summary.status.icon }}</div>
            <div class="status-message">{{ results.issues_summary.status.message }}</div>
            <p>共发现 {{ results.issues_summary.total_issues }} 个问题和建议</p>

            {% if results.issues_summary.total_issues > 0 %}
            <div class="issues-stats">
                {% if results.issues_summary.critical_count > 0 %}
                <div class="issue-stat">
                    <div class="issue-count critical-count">{{ results.issues_summary.critical_count }}</div>
                    <div>严重问题</div>
                </div>
                {% endif %}

                {% if results.issues_summary.important_count > 0 %}
                <div class="issue-stat">
                    <div class="issue-count important-count">{{ results.issues_summary.important_count }}</div>
                    <div>重要问题</div>
                </div>
                {% endif %}

                {% if results.issues_summary.minor_count > 0 %}
                <div class="issue-stat">
                    <div class="issue-count minor-count">{{ results.issues_summary.minor_count }}</div>
                    <div>轻微问题</div>
                </div>
                {% endif %}

                {% if results.issues_summary.suggestions_count > 0 %}
                <div class="issue-stat">
                    <div class="issue-count suggestions-count">{{ results.issues_summary.suggestions_count }}</div>
                    <div>优化建议</div>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- 基础SEO -->
        {% if results.basic_seo %}
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                {% if results.basic_seo.title %}
                <div class="check-item {{ 'has-issues' if results.basic_seo.title.recommendations else 'no-issues' }}">
                    <div class="check-name">
                        <span>📝 页面标题</span>
                        {% if not results.basic_seo.title.recommendations %}
                        <span class="status-badge status-good">✓ 正常</span>
                        {% else %}
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        {% endif %}
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> {{ results.basic_seo.title.content or '无' }}<br>
                        <strong>长度:</strong> {{ results.basic_seo.title.length }} 字符
                    </div>
                    {% if results.basic_seo.title.recommendations %}
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            {% for rec in results.basic_seo.title.recommendations %}
                            <li>{{ rec }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- Meta描述检查 -->
                {% if results.basic_seo.meta_description %}
                <div class="check-item {{ 'has-issues' if results.basic_seo.meta_description.recommendations else 'no-issues' }}">
                    <div class="check-name">
                        <span>📄 Meta描述</span>
                        {% if not results.basic_seo.meta_description.recommendations %}
                        <span class="status-badge status-good">✓ 正常</span>
                        {% else %}
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        {% endif %}
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> {{ results.basic_seo.meta_description.content or '无' }}<br>
                        <strong>长度:</strong> {{ results.basic_seo.meta_description.length }} 字符
                    </div>
                    {% if results.basic_seo.meta_description.recommendations %}
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            {% for rec in results.basic_seo.meta_description.recommendations %}
                            <li>{{ rec }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- 标题结构检查 -->
                {% if results.basic_seo.headings %}
                <div class="check-item {{ 'has-issues' if results.basic_seo.headings.h1.recommendations else 'no-issues' }}">
                    <div class="check-name">
                        <span>🏷️ 标题结构</span>
                        {% if not results.basic_seo.headings.h1.recommendations %}
                        <span class="status-badge status-good">✓ 正常</span>
                        {% else %}
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        {% endif %}
                    </div>
                    <div class="check-details">
                        {% for heading, data in results.basic_seo.headings.items() %}
                        <strong>{{ heading.upper() }}:</strong> {{ data.count }} 个
                        {% if data.content and data.content|length > 0 %}
                        <br>&nbsp;&nbsp;内容: {{ data.content[:2]|join(', ') }}{% if data.content|length > 2 %}...{% endif %}
                        {% endif %}
                        <br>
                        {% endfor %}
                    </div>
                    {% if results.basic_seo.headings.h1.recommendations %}
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            {% for rec in results.basic_seo.headings.h1.recommendations %}
                            <li>{{ rec }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- 图片检查 -->
                {% if results.basic_seo.images %}
                <div class="check-item {{ 'has-issues' if results.basic_seo.images.recommendations else 'no-issues' }}">
                    <div class="check-name">
                        <span>🖼️ 图片优化</span>
                        {% if not results.basic_seo.images.recommendations %}
                        <span class="status-badge status-good">✓ 正常</span>
                        {% else %}
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        {% endif %}
                    </div>
                    <div class="check-details">
                        <strong>总图片数:</strong> {{ results.basic_seo.images.total }}<br>
                        <strong>缺少alt属性:</strong> {{ results.basic_seo.images.without_alt }}
                    </div>
                    {% if results.basic_seo.images.recommendations %}
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            {% for rec in results.basic_seo.images.recommendations %}
                            <li>{{ rec }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 问题详细列表 -->
        {% if results.issues_summary and results.issues_summary.total_issues > 0 %}
        <div class="section">
            <div class="section-header">
                <h2>🔍 问题详细分析</h2>
            </div>
            <div class="section-content">
                <!-- 严重问题 -->
                {% if results.issues_summary.issues.critical %}
                <div class="issue-category critical-issues">
                    <h3>🚨 严重问题 ({{ results.issues_summary.critical_count }}个)</h3>
                    {% for issue in results.issues_summary.issues.critical %}
                    <div class="issue-item">
                        <div class="issue-header">{{ issue.type }} - {{ issue.item }}: {{ issue.issue }}</div>
                        <div class="issue-impact">影响: {{ issue.impact }}</div>
                        <div class="issue-solution">解决方案: {{ issue.solution }}</div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- 重要问题 -->
                {% if results.issues_summary.issues.important %}
                <div class="issue-category important-issues">
                    <h3>⚠️ 重要问题 ({{ results.issues_summary.important_count }}个)</h3>
                    {% for issue in results.issues_summary.issues.important %}
                    <div class="issue-item">
                        <div class="issue-header">{{ issue.type }} - {{ issue.item }}: {{ issue.issue }}</div>
                        <div class="issue-impact">影响: {{ issue.impact }}</div>
                        <div class="issue-solution">解决方案: {{ issue.solution }}</div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- 轻微问题 -->
                {% if results.issues_summary.issues.minor %}
                <div class="issue-category minor-issues">
                    <h3>🔧 轻微问题 ({{ results.issues_summary.minor_count }}个)</h3>
                    {% for issue in results.issues_summary.issues.minor %}
                    <div class="issue-item">
                        <div class="issue-header">{{ issue.type }} - {{ issue.item }}: {{ issue.issue }}</div>
                        <div class="issue-impact">影响: {{ issue.impact }}</div>
                        <div class="issue-solution">解决方案: {{ issue.solution }}</div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- 优化建议 -->
                {% if results.issues_summary.issues.suggestions %}
                <div class="issue-category suggestions">
                    <h3>💡 优化建议 ({{ results.issues_summary.suggestions_count }}个)</h3>
                    {% for issue in results.issues_summary.issues.suggestions %}
                    <div class="issue-item">
                        <div class="issue-header">{{ issue.type }} - {{ issue.item }}: {{ issue.issue }}</div>
                        <div class="issue-impact">影响: {{ issue.impact }}</div>
                        <div class="issue-solution">解决方案: {{ issue.solution }}</div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 {{ config.company }} SEO审计工具生成</p>
            <p>生成时间: {{ report_date }}</p>
        </div>
    </div>
</body>
</html>
        """
