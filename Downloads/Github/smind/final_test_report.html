
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://www.google.com</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://www.google.com</div>
            <div class="date">生成时间: 2025-05-26 09:40:13</div>
        </div>

        <!-- 问题总结概览 -->
        
        <div class="issues-overview">
            <div class="status-icon">⚠️</div>
            <div class="status-message">发现6个重要问题，建议优先处理</div>
            <p>共发现 6 个问题和建议</p>

            
            <div class="issues-stats">
                

                
                <div class="issue-stat">
                    <div class="issue-count important-count">6</div>
                    <div>重要问题</div>
                </div>
                

                

                
            </div>
            
        </div>
        

        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>📝 页面标题</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> Google<br>
                        <strong>长度:</strong> 6 字符
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>标题太短，建议至少30个字符</li>
                            
                        </ul>
                    </div>
                    
                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>📄 Meta描述</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> 无<br>
                        <strong>长度:</strong> 0 字符
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>页面缺少meta描述</li>
                            
                        </ul>
                    </div>
                    
                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>🏷️ 标题结构</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        
                        <strong>H1:</strong> 0 个
                        
                        <br>
                        
                        <strong>H2:</strong> 0 个
                        
                        <br>
                        
                        <strong>H3:</strong> 0 个
                        
                        <br>
                        
                        <strong>H4:</strong> 0 个
                        
                        <br>
                        
                        <strong>H5:</strong> 0 个
                        
                        <br>
                        
                        <strong>H6:</strong> 0 个
                        
                        <br>
                        
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>添加H1标签</li>
                            
                        </ul>
                    </div>
                    
                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>🖼️ 图片优化</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>总图片数:</strong> 9<br>
                        <strong>缺少alt属性:</strong> 9
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>9张图片缺少alt属性</li>
                            
                        </ul>
                    </div>
                    
                </div>
                
            </div>
        </div>
        

        <!-- 问题详细列表 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 问题详细分析</h2>
            </div>
            <div class="section-content">
                <!-- 严重问题 -->
                

                <!-- 重要问题 -->
                
                <div class="issue-category important-issues">
                    <h3>⚠️ 重要问题 (6个)</h3>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - 页面标题: 标题过短（6字符）</div>
                        <div class="issue-impact">影响: 可能无法充分描述页面内容</div>
                        <div class="issue-solution">解决方案: 建议标题长度至少30字符</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - Meta描述: 页面缺少Meta描述</div>
                        <div class="issue-impact">影响: 搜索引擎无法获取页面摘要信息</div>
                        <div class="issue-solution">解决方案: 添加120-160字符的页面描述</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - H1标签: 页面缺少H1标签</div>
                        <div class="issue-impact">影响: 影响页面结构和关键词权重</div>
                        <div class="issue-solution">解决方案: 添加一个包含主要关键词的H1标签</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - 图片优化: 9张图片缺少alt属性</div>
                        <div class="issue-impact">影响: 影响可访问性和图片搜索排名</div>
                        <div class="issue-solution">解决方案: 为所有图片添加描述性的alt属性</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">移动端优化 - Viewport设置: 缺少viewport meta标签</div>
                        <div class="issue-impact">影响: 移动端显示效果差</div>
                        <div class="issue-solution">解决方案: 添加viewport meta标签支持移动设备</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">内容质量 - 内容长度: 内容过少（13词）</div>
                        <div class="issue-impact">影响: 可能被搜索引擎认为内容质量低</div>
                        <div class="issue-solution">解决方案: 增加有价值的内容，建议至少300词</div>
                    </div>
                    
                </div>
                

                <!-- 轻微问题 -->
                

                <!-- 优化建议 -->
                
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-26 09:40:13</p>
        </div>
    </div>
</body>
</html>
        