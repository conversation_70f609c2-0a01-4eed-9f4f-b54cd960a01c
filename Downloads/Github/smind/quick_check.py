#!/usr/bin/env python3
"""
快速SEO检查脚本
用于快速检查公司官网或指定网站的SEO状况
"""

import sys
import os
from datetime import datetime
from seo_checker import SEOChecker
from report_generator import ReportGenerator

# 预设的常用网站（您可以修改为您公司的网站）
PRESET_WEBSITES = {
    '1': 'https://www.example.com',
    '2': 'https://www.google.com',
    '3': 'https://www.github.com',
    '4': 'https://www.stackoverflow.com',
    '5': 'https://www.w3.org'
}

def quick_check(url):
    """快速检查网站SEO"""
    print(f"\n🔍 正在检查: {url}")
    print("-" * 50)

    try:
        # 创建检查器并执行检查
        checker = SEOChecker(url)
        results = checker.check_all()

        if results is None:
            print("❌ 检查失败: 无法访问网站")
            return False

        # 显示关键指标
        if 'issues_summary' in results:
            summary = results['issues_summary']

            print(f"{summary['status']['icon']} {summary['status']['message']}")
            print(f"📋 问题统计: 严重{summary['critical_count']}个, 重要{summary['important_count']}个, 轻微{summary['minor_count']}个, 建议{summary['suggestions_count']}个")

        # 显示主要问题
        print("\n🔍 主要发现:")

        if 'issues_summary' in results:
            issues = results['issues_summary']['issues']

            # 显示严重问题
            if issues['critical']:
                for issue in issues['critical'][:3]:  # 只显示前3个
                    print(f"  🚨 {issue['item']}: {issue['issue']}")

            # 显示重要问题
            if issues['important']:
                for issue in issues['important'][:3]:  # 只显示前3个
                    print(f"  ⚠️  {issue['item']}: {issue['issue']}")

            # 如果没有严重和重要问题，显示轻微问题
            if not issues['critical'] and not issues['important'] and issues['minor']:
                for issue in issues['minor'][:3]:  # 只显示前3个
                    print(f"  🔧 {issue['item']}: {issue['issue']}")

            # 如果没有任何问题，显示建议
            if not issues['critical'] and not issues['important'] and not issues['minor'] and issues['suggestions']:
                for issue in issues['suggestions'][:3]:  # 只显示前3个
                    print(f"  💡 {issue['item']}: {issue['issue']}")

        if not results.get('issues_summary', {}).get('total_issues', 0):
            print("  ✅ 未发现明显问题，网站SEO状况良好")

        # 生成报告
        domain = url.replace('https://', '').replace('http://', '').replace('/', '_')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"quick_seo_report_{domain}_{timestamp}.html"

        generator = ReportGenerator()
        generator.generate_html_report(results, url, output_file)

        print(f"\n📄 详细报告已保存: {output_file}")
        print("💡 请在浏览器中打开查看完整分析结果")

        return True

    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 SEO快速检查工具")
    print("=" * 50)

    if len(sys.argv) > 1:
        # 如果提供了命令行参数，直接检查该URL
        url = sys.argv[1]
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        quick_check(url)
        return

    while True:
        print("\n请选择检查方式:")
        print("1. 输入自定义URL")
        print("2. 从预设网站中选择")
        print("3. 退出")

        choice = input("\n请输入选择 (1-3): ").strip()

        if choice == '1':
            url = input("请输入网站URL: ").strip()
            if not url:
                print("请输入有效的URL")
                continue

            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url

            quick_check(url)

        elif choice == '2':
            print("\n预设网站:")
            for key, url in PRESET_WEBSITES.items():
                print(f"  {key}. {url}")

            site_choice = input("\n请选择网站 (1-5): ").strip()

            if site_choice in PRESET_WEBSITES:
                quick_check(PRESET_WEBSITES[site_choice])
            else:
                print("无效选择")

        elif choice == '3':
            print("👋 再见!")
            break

        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
