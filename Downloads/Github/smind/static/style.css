
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .url {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .header .date {
            opacity: 0.8;
            font-size: 0.9em;
        }

        .issues-overview {
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .status-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .status-message {
            font-size: 1.5em;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }

        .issues-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .issue-stat {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .issue-count {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .critical-count { color: #dc3545; }
        .important-count { color: #fd7e14; }
        .minor-count { color: #ffc107; }
        .suggestions-count { color: #17a2b8; }

        .section {
            margin: 30px;
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            border-left: 4px solid #007bff;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.3em;
            font-weight: 600;
        }

        .section-content {
            background: white;
            padding: 20px;
            border-radius: 0 0 10px 10px;
            border: 1px solid #e9ecef;
            border-top: none;
        }

        .check-item {
            background: #f8f9fa;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .check-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .check-item.has-issues {
            border-left-color: #ffc107;
            background: #fff8e1;
        }

        .check-item.no-issues {
            border-left-color: #28a745;
            background: #f8fff9;
        }

        .check-item.critical {
            border-left-color: #dc3545;
            background: #fdf2f2;
        }

        .check-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .check-details {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 0.9em;
            border: 1px solid #e9ecef;
            line-height: 1.8;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-good {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .recommendations {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .recommendations h4 {
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .recommendations ul {
            list-style: none;
            padding: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
            color: #1565c0;
        }

        .recommendations li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }

        .issue-category {
            margin-bottom: 30px;
        }

        .issue-category h3 {
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
        }

        .critical-issues h3 { background: #dc3545; }
        .important-issues h3 { background: #fd7e14; }
        .minor-issues h3 { background: #ffc107; color: #333; }
        .suggestions h3 { background: #17a2b8; }

        .issue-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .issue-header {
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .issue-impact {
            color: #6c757d;
            margin-bottom: 10px;
            font-style: italic;
        }

        .issue-solution {
            color: #28a745;
            font-weight: 500;
            padding: 10px;
            background: #f8fff9;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                border-radius: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .issues-overview {
                padding: 30px 20px;
            }

            .issues-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .section {
                margin: 20px 15px;
            }

            .check-name {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        