
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://example.com</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://example.com</div>
            <div class="date">生成时间: 2025-05-26 09:37:21</div>
        </div>

        <!-- 问题总结概览 -->
        
        <div class="issues-overview">
            <div class="status-icon">⚠️</div>
            <div class="status-message">发现4个重要问题，建议优先处理</div>
            <p>共发现 6 个问题和建议</p>

            
            <div class="issues-stats">
                

                
                <div class="issue-stat">
                    <div class="issue-count important-count">4</div>
                    <div>重要问题</div>
                </div>
                

                

                
                <div class="issue-stat">
                    <div class="issue-count suggestions-count">2</div>
                    <div>优化建议</div>
                </div>
                
            </div>
            
        </div>
        

        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>📝 页面标题</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> Example Domain<br>
                        <strong>长度:</strong> 14 字符
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>标题太短，建议至少30个字符</li>
                            
                        </ul>
                    </div>
                    
                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item has-issues">
                    <div class="check-name">
                        <span>📄 Meta描述</span>
                        
                        <span class="status-badge status-warning">⚠ 需要优化</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>内容:</strong> 无<br>
                        <strong>长度:</strong> 0 字符
                    </div>
                    
                    <div class="recommendations">
                        <h4>💡 优化建议</h4>
                        <ul>
                            
                            <li>页面缺少meta描述</li>
                            
                        </ul>
                    </div>
                    
                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item no-issues">
                    <div class="check-name">
                        <span>🏷️ 标题结构</span>
                        
                        <span class="status-badge status-good">✓ 正常</span>
                        
                    </div>
                    <div class="check-details">
                        
                        <strong>H1:</strong> 1 个
                        
                        <br>&nbsp;&nbsp;内容: Example Domain
                        
                        <br>
                        
                        <strong>H2:</strong> 0 个
                        
                        <br>
                        
                        <strong>H3:</strong> 0 个
                        
                        <br>
                        
                        <strong>H4:</strong> 0 个
                        
                        <br>
                        
                        <strong>H5:</strong> 0 个
                        
                        <br>
                        
                        <strong>H6:</strong> 0 个
                        
                        <br>
                        
                    </div>
                    
                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item no-issues">
                    <div class="check-name">
                        <span>🖼️ 图片优化</span>
                        
                        <span class="status-badge status-good">✓ 正常</span>
                        
                    </div>
                    <div class="check-details">
                        <strong>总图片数:</strong> 0<br>
                        <strong>缺少alt属性:</strong> 0
                    </div>
                    
                </div>
                
            </div>
        </div>
        

        <!-- 问题详细列表 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 问题详细分析</h2>
            </div>
            <div class="section-content">
                <!-- 严重问题 -->
                

                <!-- 重要问题 -->
                
                <div class="issue-category important-issues">
                    <h3>⚠️ 重要问题 (4个)</h3>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - 页面标题: 标题过短（14字符）</div>
                        <div class="issue-impact">影响: 可能无法充分描述页面内容</div>
                        <div class="issue-solution">解决方案: 建议标题长度至少30字符</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">基础SEO - Meta描述: 页面缺少Meta描述</div>
                        <div class="issue-impact">影响: 搜索引擎无法获取页面摘要信息</div>
                        <div class="issue-solution">解决方案: 添加120-160字符的页面描述</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">技术SEO - 内部链接: 内部链接过少（0个）</div>
                        <div class="issue-impact">影响: 影响网站结构和页面权重传递</div>
                        <div class="issue-solution">解决方案: 增加相关页面的内部链接</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">内容质量 - 内容长度: 内容过少（30词）</div>
                        <div class="issue-impact">影响: 可能被搜索引擎认为内容质量低</div>
                        <div class="issue-solution">解决方案: 增加有价值的内容，建议至少300词</div>
                    </div>
                    
                </div>
                

                <!-- 轻微问题 -->
                

                <!-- 优化建议 -->
                
                <div class="issue-category suggestions">
                    <h3>💡 优化建议 (2个)</h3>
                    
                    <div class="issue-item">
                        <div class="issue-header">技术SEO - Robots.txt: 网站缺少robots.txt文件</div>
                        <div class="issue-impact">影响: 无法指导搜索引擎爬虫行为</div>
                        <div class="issue-solution">解决方案: 创建robots.txt文件指导搜索引擎抓取</div>
                    </div>
                    
                    <div class="issue-item">
                        <div class="issue-header">技术SEO - XML网站地图: 网站缺少XML网站地图</div>
                        <div class="issue-impact">影响: 搜索引擎难以发现所有页面</div>
                        <div class="issue-solution">解决方案: 创建并提交XML网站地图到搜索引擎</div>
                    </div>
                    
                </div>
                
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-26 09:37:21</p>
        </div>
    </div>
</body>
</html>
        