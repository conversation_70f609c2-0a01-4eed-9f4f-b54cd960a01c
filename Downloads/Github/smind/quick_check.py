#!/usr/bin/env python3
"""
快速SEO检查脚本
用于快速检查公司官网或指定网站的SEO状况
"""

import sys
import os
from datetime import datetime
from seo_checker import SEOChecker
from report_generator import ReportGenerator

# 预设的常用网站（您可以修改为您公司的网站）
PRESET_WEBSITES = {
    '1': 'https://www.example.com',
    '2': 'https://www.google.com',
    '3': 'https://www.github.com',
    '4': 'https://www.stackoverflow.com',
    '5': 'https://www.w3.org'
}

def quick_check(url):
    """快速检查网站SEO"""
    print(f"\n🔍 正在检查: {url}")
    print("-" * 50)
    
    try:
        # 创建检查器并执行检查
        checker = SEOChecker(url)
        results = checker.check_all()
        
        if results is None:
            print("❌ 检查失败: 无法访问网站")
            return False
        
        # 显示关键指标
        if 'total_score' in results:
            score = results['total_score']['score']
            grade = results['total_score']['grade']
            
            print(f"📊 总体评分: {score:.1f} (等级: {grade})")
            
            # 根据评分给出简要评价
            if score >= 90:
                print("🎉 优秀！您的网站SEO表现很好")
            elif score >= 80:
                print("👍 良好！还有一些改进空间")
            elif score >= 70:
                print("⚠️  一般，建议进行优化")
            elif score >= 60:
                print("🔧 需要改进，存在多个SEO问题")
            else:
                print("🚨 较差，急需SEO优化")
        
        # 显示主要问题
        print("\n🔍 主要发现:")
        
        # 基础SEO问题
        if 'basic_seo' in results:
            basic = results['basic_seo']
            
            if basic.get('title', {}).get('score', 100) < 70:
                print("  ⚠️  页面标题需要优化")
            
            if basic.get('meta_description', {}).get('score', 100) < 70:
                print("  ⚠️  Meta描述缺失或需要改进")
            
            if basic.get('headings', {}).get('h1', {}).get('score', 100) < 70:
                print("  ⚠️  H1标签使用不当")
            
            if basic.get('images', {}).get('score', 100) < 70:
                print("  ⚠️  图片缺少alt属性")
        
        # 技术SEO问题
        if 'technical_seo' in results:
            tech = results['technical_seo']
            
            if not tech.get('robots_txt', {}).get('exists', True):
                print("  ⚠️  缺少robots.txt文件")
            
            if not tech.get('sitemap', {}).get('exists', True):
                print("  ⚠️  缺少XML网站地图")
            
            if tech.get('internal_links', {}).get('count', 0) < 5:
                print("  ⚠️  内部链接过少")
        
        # 性能问题
        if 'performance' in results:
            perf = results['performance']
            
            load_time = perf.get('load_time', {}).get('time', 0)
            if load_time > 3:
                print(f"  ⚠️  页面加载时间较慢 ({load_time:.1f}秒)")
            
            page_size = perf.get('page_size', {}).get('size', 0)
            if page_size > 1000:
                print(f"  ⚠️  页面大小较大 ({page_size:.1f}KB)")
        
        # 安全问题
        if 'security' in results:
            security = results['security']
            
            if not security.get('https', {}).get('enabled', True):
                print("  🚨 未启用HTTPS加密")
        
        # 生成报告
        domain = url.replace('https://', '').replace('http://', '').replace('/', '_')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"quick_seo_report_{domain}_{timestamp}.html"
        
        generator = ReportGenerator()
        generator.generate_html_report(results, url, output_file)
        
        print(f"\n📄 详细报告已保存: {output_file}")
        print("💡 请在浏览器中打开查看完整分析结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 SEO快速检查工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # 如果提供了命令行参数，直接检查该URL
        url = sys.argv[1]
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        quick_check(url)
        return
    
    while True:
        print("\n请选择检查方式:")
        print("1. 输入自定义URL")
        print("2. 从预设网站中选择")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            url = input("请输入网站URL: ").strip()
            if not url:
                print("请输入有效的URL")
                continue
            
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            quick_check(url)
        
        elif choice == '2':
            print("\n预设网站:")
            for key, url in PRESET_WEBSITES.items():
                print(f"  {key}. {url}")
            
            site_choice = input("\n请选择网站 (1-5): ").strip()
            
            if site_choice in PRESET_WEBSITES:
                quick_check(PRESET_WEBSITES[site_choice])
            else:
                print("无效选择")
        
        elif choice == '3':
            print("👋 再见!")
            break
        
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
