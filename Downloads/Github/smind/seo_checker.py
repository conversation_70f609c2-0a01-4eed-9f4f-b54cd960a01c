import requests
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import validators
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import config

class SEOChecker:
    def __init__(self, url):
        self.url = url
        self.domain = urlparse(url).netloc
        self.results = {}
        self.soup = None
        self.response = None

    def check_all(self):
        """执行所有SEO检查"""
        print(f"开始检查网站: {self.url}")

        # 获取页面内容
        if not self._fetch_page():
            return None

        # 执行各项检查
        self._check_basic_seo()
        self._check_technical_seo()
        self._check_content_analysis()
        self._check_performance()
        self._check_mobile_friendly()
        self._check_security()

        # 计算总分
        self._calculate_total_score()

        return self.results

    def _fetch_page(self):
        """获取页面内容"""
        try:
            headers = {'User-Agent': config.USER_AGENT}
            self.response = requests.get(self.url, headers=headers, timeout=config.REQUEST_TIMEOUT)
            self.response.raise_for_status()

            self.soup = BeautifulSoup(self.response.content, 'html.parser')
            return True
        except Exception as e:
            print(f"获取页面失败: {e}")
            return False

    def _check_basic_seo(self):
        """基础SEO检查"""
        basic_seo = {}

        # 检查标题
        title_tag = self.soup.find('title')
        if title_tag:
            title = title_tag.get_text().strip()
            basic_seo['title'] = {
                'content': title,
                'length': len(title),
                'score': self._score_title(title),
                'recommendations': self._get_title_recommendations(title)
            }
        else:
            basic_seo['title'] = {
                'content': '',
                'length': 0,
                'score': 0,
                'recommendations': ['页面缺少标题标签']
            }

        # 检查meta描述
        meta_desc = self.soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            description = meta_desc.get('content', '').strip()
            basic_seo['meta_description'] = {
                'content': description,
                'length': len(description),
                'score': self._score_description(description),
                'recommendations': self._get_description_recommendations(description)
            }
        else:
            basic_seo['meta_description'] = {
                'content': '',
                'length': 0,
                'score': 0,
                'recommendations': ['页面缺少meta描述']
            }

        # 检查meta关键词
        meta_keywords = self.soup.find('meta', attrs={'name': 'keywords'})
        basic_seo['meta_keywords'] = {
            'content': meta_keywords.get('content', '') if meta_keywords else '',
            'score': 50 if meta_keywords else 0,
            'recommendations': [] if meta_keywords else ['建议添加meta关键词']
        }

        # 检查标题结构
        basic_seo['headings'] = self._check_headings()

        # 检查图片
        basic_seo['images'] = self._check_images()

        self.results['basic_seo'] = basic_seo

    def _check_technical_seo(self):
        """技术SEO检查"""
        technical_seo = {}

        # 检查robots.txt
        technical_seo['robots_txt'] = self._check_robots_txt()

        # 检查sitemap
        technical_seo['sitemap'] = self._check_sitemap()

        # 检查URL结构
        technical_seo['url_structure'] = self._check_url_structure()

        # 检查内部链接
        technical_seo['internal_links'] = self._check_internal_links()

        # 检查外部链接
        technical_seo['external_links'] = self._check_external_links()

        self.results['technical_seo'] = technical_seo

    def _check_content_analysis(self):
        """内容分析"""
        content_analysis = {}

        # 获取页面文本内容
        text_content = self.soup.get_text()
        word_count = len(text_content.split())

        content_analysis['word_count'] = {
            'count': word_count,
            'score': self._score_content_length(word_count),
            'recommendations': self._get_content_recommendations(word_count)
        }

        # 检查重复内容（简单检查）
        content_analysis['duplicate_content'] = self._check_duplicate_content()

        self.results['content_analysis'] = content_analysis

    def _check_performance(self):
        """性能检查"""
        performance = {}

        # 页面加载时间
        start_time = time.time()
        try:
            response = requests.get(self.url, timeout=config.REQUEST_TIMEOUT)
            load_time = time.time() - start_time
            performance['load_time'] = {
                'time': round(load_time, 2),
                'score': self._score_load_time(load_time),
                'recommendations': self._get_performance_recommendations(load_time)
            }
        except Exception as e:
            performance['load_time'] = {
                'time': 0,
                'score': 0,
                'recommendations': [f'无法测试加载时间: {e}']
            }

        # 页面大小
        if self.response:
            page_size = len(self.response.content) / 1024  # KB
            performance['page_size'] = {
                'size': round(page_size, 2),
                'score': self._score_page_size(page_size),
                'recommendations': self._get_size_recommendations(page_size)
            }

        self.results['performance'] = performance

    def _check_mobile_friendly(self):
        """移动端友好性检查"""
        mobile = {}

        # 检查viewport meta标签
        viewport = self.soup.find('meta', attrs={'name': 'viewport'})
        mobile['viewport'] = {
            'exists': bool(viewport),
            'content': viewport.get('content', '') if viewport else '',
            'score': 100 if viewport else 0,
            'recommendations': [] if viewport else ['添加viewport meta标签以支持移动设备']
        }

        # 检查响应式设计（简单检查CSS媒体查询）
        mobile['responsive'] = self._check_responsive_design()

        self.results['mobile'] = mobile

    def _check_security(self):
        """安全检查"""
        security = {}

        # 检查HTTPS
        is_https = self.url.startswith('https://')
        security['https'] = {
            'enabled': is_https,
            'score': 100 if is_https else 0,
            'recommendations': [] if is_https else ['建议使用HTTPS协议保护用户数据']
        }

        self.results['security'] = security

    # 辅助方法
    def _score_title(self, title):
        """评分标题"""
        if not title:
            return 0
        length = len(title)
        if config.MIN_TITLE_LENGTH <= length <= config.MAX_TITLE_LENGTH:
            return 100
        elif length < config.MIN_TITLE_LENGTH:
            return max(0, (length / config.MIN_TITLE_LENGTH) * 100)
        else:
            return max(0, 100 - ((length - config.MAX_TITLE_LENGTH) * 2))

    def _get_title_recommendations(self, title):
        """获取标题建议"""
        recommendations = []
        if not title:
            recommendations.append("添加页面标题")
        elif len(title) < config.MIN_TITLE_LENGTH:
            recommendations.append(f"标题太短，建议至少{config.MIN_TITLE_LENGTH}个字符")
        elif len(title) > config.MAX_TITLE_LENGTH:
            recommendations.append(f"标题太长，建议不超过{config.MAX_TITLE_LENGTH}个字符")
        return recommendations

    def _score_description(self, description):
        """评分描述"""
        if not description:
            return 0
        length = len(description)
        if config.MIN_DESCRIPTION_LENGTH <= length <= config.MAX_DESCRIPTION_LENGTH:
            return 100
        elif length < config.MIN_DESCRIPTION_LENGTH:
            return max(0, (length / config.MIN_DESCRIPTION_LENGTH) * 100)
        else:
            return max(0, 100 - ((length - config.MAX_DESCRIPTION_LENGTH) * 2))

    def _get_description_recommendations(self, description):
        """获取描述建议"""
        recommendations = []
        if not description:
            recommendations.append("添加meta描述")
        elif len(description) < config.MIN_DESCRIPTION_LENGTH:
            recommendations.append(f"描述太短，建议至少{config.MIN_DESCRIPTION_LENGTH}个字符")
        elif len(description) > config.MAX_DESCRIPTION_LENGTH:
            recommendations.append(f"描述太长，建议不超过{config.MAX_DESCRIPTION_LENGTH}个字符")
        return recommendations

    def _check_headings(self):
        """检查标题结构"""
        headings = {}
        h1_tags = self.soup.find_all('h1')

        headings['h1'] = {
            'count': len(h1_tags),
            'content': [h.get_text().strip() for h in h1_tags],
            'score': 100 if len(h1_tags) == 1 else (50 if len(h1_tags) > 1 else 0),
            'recommendations': self._get_heading_recommendations(h1_tags)
        }

        # 检查其他标题标签
        for i in range(2, 7):
            h_tags = self.soup.find_all(f'h{i}')
            headings[f'h{i}'] = {
                'count': len(h_tags),
                'content': [h.get_text().strip() for h in h_tags[:5]]  # 只显示前5个
            }

        return headings

    def _get_heading_recommendations(self, h1_tags):
        """获取标题建议"""
        recommendations = []
        if len(h1_tags) == 0:
            recommendations.append("添加H1标签")
        elif len(h1_tags) > 1:
            recommendations.append("每个页面应该只有一个H1标签")
        return recommendations

    def _check_images(self):
        """检查图片"""
        images = self.soup.find_all('img')
        total_images = len(images)
        images_without_alt = len([img for img in images if not img.get('alt')])

        return {
            'total': total_images,
            'without_alt': images_without_alt,
            'score': max(0, 100 - (images_without_alt / max(total_images, 1)) * 100),
            'recommendations': [f"{images_without_alt}张图片缺少alt属性"] if images_without_alt > 0 else []
        }

    def _check_robots_txt(self):
        """检查robots.txt"""
        try:
            robots_url = urljoin(self.url, '/robots.txt')
            response = requests.get(robots_url, timeout=10)
            exists = response.status_code == 200
            return {
                'exists': exists,
                'score': 100 if exists else 50,
                'recommendations': [] if exists else ['建议添加robots.txt文件']
            }
        except:
            return {
                'exists': False,
                'score': 50,
                'recommendations': ['无法访问robots.txt文件']
            }

    def _check_sitemap(self):
        """检查sitemap"""
        sitemap_urls = ['/sitemap.xml', '/sitemap_index.xml']
        for sitemap_url in sitemap_urls:
            try:
                full_url = urljoin(self.url, sitemap_url)
                response = requests.get(full_url, timeout=10)
                if response.status_code == 200:
                    return {
                        'exists': True,
                        'url': full_url,
                        'score': 100,
                        'recommendations': []
                    }
            except:
                continue

        return {
            'exists': False,
            'score': 50,
            'recommendations': ['建议添加XML网站地图']
        }

    def _check_url_structure(self):
        """检查URL结构"""
        parsed_url = urlparse(self.url)
        path = parsed_url.path

        # 检查URL是否友好
        is_friendly = bool(re.match(r'^[a-zA-Z0-9\-/]*$', path))
        has_parameters = bool(parsed_url.query)

        score = 100
        recommendations = []

        if not is_friendly:
            score -= 30
            recommendations.append("URL包含特殊字符，建议使用友好的URL结构")

        if has_parameters:
            score -= 20
            recommendations.append("URL包含查询参数，可能影响SEO")

        return {
            'friendly': is_friendly,
            'has_parameters': has_parameters,
            'score': max(0, score),
            'recommendations': recommendations
        }

    def _check_internal_links(self):
        """检查内部链接"""
        links = self.soup.find_all('a', href=True)
        internal_links = []

        for link in links:
            href = link['href']
            if href.startswith('/') or self.domain in href:
                internal_links.append(href)

        return {
            'count': len(internal_links),
            'score': min(100, len(internal_links) * 5),  # 每个内部链接5分，最高100分
            'recommendations': ['增加内部链接以改善网站结构'] if len(internal_links) < 10 else []
        }

    def _check_external_links(self):
        """检查外部链接"""
        links = self.soup.find_all('a', href=True)
        external_links = []

        for link in links:
            href = link['href']
            if href.startswith('http') and self.domain not in href:
                external_links.append(href)

        return {
            'count': len(external_links),
            'score': 100,  # 外部链接存在即可
            'recommendations': []
        }

    def _score_content_length(self, word_count):
        """评分内容长度"""
        if word_count >= config.OPTIMAL_CONTENT_LENGTH:
            return 100
        elif word_count >= config.MIN_CONTENT_LENGTH:
            return (word_count / config.OPTIMAL_CONTENT_LENGTH) * 100
        else:
            return max(0, (word_count / config.MIN_CONTENT_LENGTH) * 50)

    def _get_content_recommendations(self, word_count):
        """获取内容建议"""
        recommendations = []
        if word_count < config.MIN_CONTENT_LENGTH:
            recommendations.append(f"内容太少，建议至少{config.MIN_CONTENT_LENGTH}个词")
        elif word_count < config.OPTIMAL_CONTENT_LENGTH:
            recommendations.append(f"建议增加内容到{config.OPTIMAL_CONTENT_LENGTH}个词以上")
        return recommendations

    def _check_duplicate_content(self):
        """检查重复内容（简单实现）"""
        # 这里只是一个简单的实现，实际应用中可能需要更复杂的算法
        return {
            'detected': False,
            'score': 100,
            'recommendations': []
        }

    def _score_load_time(self, load_time):
        """评分加载时间"""
        if load_time <= 2:
            return 100
        elif load_time <= 4:
            return 80
        elif load_time <= 6:
            return 60
        else:
            return max(0, 60 - (load_time - 6) * 10)

    def _get_performance_recommendations(self, load_time):
        """获取性能建议"""
        recommendations = []
        if load_time > 6:
            recommendations.append("页面加载时间过长，建议优化")
        elif load_time > 4:
            recommendations.append("页面加载时间较长，可以进一步优化")
        elif load_time > 2:
            recommendations.append("页面加载时间良好，可以微调优化")
        return recommendations

    def _score_page_size(self, page_size):
        """评分页面大小"""
        if page_size <= 500:  # 500KB
            return 100
        elif page_size <= 1000:  # 1MB
            return 80
        elif page_size <= 2000:  # 2MB
            return 60
        else:
            return max(0, 60 - (page_size - 2000) / 100)

    def _get_size_recommendations(self, page_size):
        """获取大小建议"""
        recommendations = []
        if page_size > 2000:
            recommendations.append("页面过大，建议压缩图片和资源")
        elif page_size > 1000:
            recommendations.append("页面较大，建议优化资源")
        return recommendations

    def _check_responsive_design(self):
        """检查响应式设计"""
        # 简单检查CSS中是否有媒体查询
        style_tags = self.soup.find_all('style')
        link_tags = self.soup.find_all('link', rel='stylesheet')

        has_media_query = False

        # 检查内联样式
        for style in style_tags:
            if '@media' in style.get_text():
                has_media_query = True
                break

        return {
            'detected': has_media_query,
            'score': 100 if has_media_query else 50,
            'recommendations': [] if has_media_query else ['建议添加响应式设计支持']
        }

    def _calculate_total_score(self):
        """计算总分"""
        total_score = 0
        max_score = 0

        # 基础SEO分数
        if 'basic_seo' in self.results:
            basic = self.results['basic_seo']
            total_score += basic.get('title', {}).get('score', 0) * config.SCORE_WEIGHTS['title'] / 100
            total_score += basic.get('meta_description', {}).get('score', 0) * config.SCORE_WEIGHTS['meta_description'] / 100
            total_score += basic.get('headings', {}).get('h1', {}).get('score', 0) * config.SCORE_WEIGHTS['headings'] / 100
            total_score += basic.get('images', {}).get('score', 0) * config.SCORE_WEIGHTS['images'] / 100
            total_score += basic.get('meta_keywords', {}).get('score', 0) * config.SCORE_WEIGHTS['meta_keywords'] / 100

            max_score += config.SCORE_WEIGHTS['title']
            max_score += config.SCORE_WEIGHTS['meta_description']
            max_score += config.SCORE_WEIGHTS['headings']
            max_score += config.SCORE_WEIGHTS['images']
            max_score += config.SCORE_WEIGHTS['meta_keywords']

        # 技术SEO分数
        if 'technical_seo' in self.results:
            tech = self.results['technical_seo']
            total_score += tech.get('internal_links', {}).get('score', 0) * config.SCORE_WEIGHTS['links'] / 100
            total_score += tech.get('robots_txt', {}).get('score', 0) * config.SCORE_WEIGHTS['robots_txt'] / 100

            max_score += config.SCORE_WEIGHTS['links']
            max_score += config.SCORE_WEIGHTS['robots_txt']

        # 性能分数
        if 'performance' in self.results:
            perf = self.results['performance']
            total_score += perf.get('load_time', {}).get('score', 0) * config.SCORE_WEIGHTS['page_speed'] / 100
            max_score += config.SCORE_WEIGHTS['page_speed']

        # 移动端分数
        if 'mobile' in self.results:
            mobile = self.results['mobile']
            total_score += mobile.get('viewport', {}).get('score', 0) * config.SCORE_WEIGHTS['mobile_friendly'] / 100
            max_score += config.SCORE_WEIGHTS['mobile_friendly']

        # 安全分数
        if 'security' in self.results:
            security = self.results['security']
            total_score += security.get('https', {}).get('score', 0) * config.SCORE_WEIGHTS['ssl'] / 100
            max_score += config.SCORE_WEIGHTS['ssl']

        # 内容分数
        if 'content_analysis' in self.results:
            content = self.results['content_analysis']
            total_score += content.get('word_count', {}).get('score', 0) * config.SCORE_WEIGHTS['content_length'] / 100
            max_score += config.SCORE_WEIGHTS['content_length']

        # 计算百分比分数
        final_score = (total_score / max_score * 100) if max_score > 0 else 0

        self.results['total_score'] = {
            'score': round(final_score, 1),
            'grade': self._get_grade(final_score),
            'max_possible': max_score,
            'achieved': round(total_score, 1)
        }

    def _get_grade(self, score):
        """根据分数获取等级"""
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'
