import requests
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import validators
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import config

class SEOChecker:
    def __init__(self, url):
        self.url = url
        self.domain = urlparse(url).netloc
        self.results = {}
        self.soup = None
        self.response = None

    def check_all(self):
        """执行所有SEO检查"""
        print(f"开始检查网站: {self.url}")

        # 获取页面内容
        if not self._fetch_page():
            return None

        # 执行各项检查
        self._check_basic_seo()
        self._check_technical_seo()
        self._check_content_analysis()
        self._check_performance()
        self._check_mobile_friendly()
        self._check_security()

        # 生成问题总结
        self._generate_issues_summary()

        return self.results

    def _fetch_page(self):
        """获取页面内容"""
        try:
            headers = {'User-Agent': config.USER_AGENT}
            self.response = requests.get(self.url, headers=headers, timeout=config.REQUEST_TIMEOUT)
            self.response.raise_for_status()

            self.soup = BeautifulSoup(self.response.content, 'html.parser')
            return True
        except Exception as e:
            print(f"获取页面失败: {e}")
            return False

    def _check_basic_seo(self):
        """基础SEO检查"""
        basic_seo = {}

        # 检查标题
        title_tag = self.soup.find('title')
        if title_tag:
            title = title_tag.get_text().strip()
            basic_seo['title'] = {
                'content': title,
                'length': len(title),
                'recommendations': self._get_title_recommendations(title)
            }
        else:
            basic_seo['title'] = {
                'content': '',
                'length': 0,
                'recommendations': ['页面缺少标题标签']
            }

        # 检查meta描述
        meta_desc = self.soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            description = meta_desc.get('content', '').strip()
            basic_seo['meta_description'] = {
                'content': description,
                'length': len(description),
                'recommendations': self._get_description_recommendations(description)
            }
        else:
            basic_seo['meta_description'] = {
                'content': '',
                'length': 0,
                'recommendations': ['页面缺少meta描述']
            }

        # 检查meta关键词
        meta_keywords = self.soup.find('meta', attrs={'name': 'keywords'})
        basic_seo['meta_keywords'] = {
            'content': meta_keywords.get('content', '') if meta_keywords else '',
            'recommendations': [] if meta_keywords else ['建议添加meta关键词']
        }

        # 检查标题结构
        basic_seo['headings'] = self._check_headings()

        # 检查图片
        basic_seo['images'] = self._check_images()

        self.results['basic_seo'] = basic_seo

    def _check_technical_seo(self):
        """技术SEO检查"""
        technical_seo = {}

        # 检查robots.txt
        technical_seo['robots_txt'] = self._check_robots_txt()

        # 检查sitemap
        technical_seo['sitemap'] = self._check_sitemap()

        # 检查URL结构
        technical_seo['url_structure'] = self._check_url_structure()

        # 检查内部链接
        technical_seo['internal_links'] = self._check_internal_links()

        # 检查外部链接
        technical_seo['external_links'] = self._check_external_links()

        self.results['technical_seo'] = technical_seo

    def _check_content_analysis(self):
        """内容分析"""
        content_analysis = {}

        # 获取页面文本内容
        text_content = self.soup.get_text()
        word_count = len(text_content.split())

        content_analysis['word_count'] = {
            'count': word_count,
            'recommendations': self._get_content_recommendations(word_count)
        }

        # 检查重复内容（简单检查）
        content_analysis['duplicate_content'] = self._check_duplicate_content()

        self.results['content_analysis'] = content_analysis

    def _check_performance(self):
        """性能检查"""
        performance = {}

        # 页面加载时间
        start_time = time.time()
        try:
            response = requests.get(self.url, timeout=config.REQUEST_TIMEOUT)
            load_time = time.time() - start_time
            performance['load_time'] = {
                'time': round(load_time, 2),
                'recommendations': self._get_performance_recommendations(load_time)
            }
        except Exception as e:
            performance['load_time'] = {
                'time': 0,
                'recommendations': [f'无法测试加载时间: {e}']
            }

        # 页面大小
        if self.response:
            page_size = len(self.response.content) / 1024  # KB
            performance['page_size'] = {
                'size': round(page_size, 2),
                'recommendations': self._get_size_recommendations(page_size)
            }

        self.results['performance'] = performance

    def _check_mobile_friendly(self):
        """移动端友好性检查"""
        mobile = {}

        # 检查viewport meta标签
        viewport = self.soup.find('meta', attrs={'name': 'viewport'})
        mobile['viewport'] = {
            'exists': bool(viewport),
            'content': viewport.get('content', '') if viewport else '',
            'recommendations': [] if viewport else ['添加viewport meta标签以支持移动设备']
        }

        # 检查响应式设计（简单检查CSS媒体查询）
        mobile['responsive'] = self._check_responsive_design()

        self.results['mobile'] = mobile

    def _check_security(self):
        """安全检查"""
        security = {}

        # 检查HTTPS
        is_https = self.url.startswith('https://')
        security['https'] = {
            'enabled': is_https,
            'recommendations': [] if is_https else ['建议使用HTTPS协议保护用户数据']
        }

        self.results['security'] = security

    # 辅助方法

    def _get_title_recommendations(self, title):
        """获取标题建议"""
        recommendations = []
        if not title:
            recommendations.append("添加页面标题")
        elif len(title) < config.MIN_TITLE_LENGTH:
            recommendations.append(f"标题太短，建议至少{config.MIN_TITLE_LENGTH}个字符")
        elif len(title) > config.MAX_TITLE_LENGTH:
            recommendations.append(f"标题太长，建议不超过{config.MAX_TITLE_LENGTH}个字符")
        return recommendations



    def _get_description_recommendations(self, description):
        """获取描述建议"""
        recommendations = []
        if not description:
            recommendations.append("添加meta描述")
        elif len(description) < config.MIN_DESCRIPTION_LENGTH:
            recommendations.append(f"描述太短，建议至少{config.MIN_DESCRIPTION_LENGTH}个字符")
        elif len(description) > config.MAX_DESCRIPTION_LENGTH:
            recommendations.append(f"描述太长，建议不超过{config.MAX_DESCRIPTION_LENGTH}个字符")
        return recommendations

    def _check_headings(self):
        """检查标题结构"""
        headings = {}
        h1_tags = self.soup.find_all('h1')

        headings['h1'] = {
            'count': len(h1_tags),
            'content': [h.get_text().strip() for h in h1_tags],
            'recommendations': self._get_heading_recommendations(h1_tags)
        }

        # 检查其他标题标签
        for i in range(2, 7):
            h_tags = self.soup.find_all(f'h{i}')
            headings[f'h{i}'] = {
                'count': len(h_tags),
                'content': [h.get_text().strip() for h in h_tags[:5]]  # 只显示前5个
            }

        return headings

    def _get_heading_recommendations(self, h1_tags):
        """获取标题建议"""
        recommendations = []
        if len(h1_tags) == 0:
            recommendations.append("添加H1标签")
        elif len(h1_tags) > 1:
            recommendations.append("每个页面应该只有一个H1标签")
        return recommendations

    def _check_images(self):
        """检查图片"""
        images = self.soup.find_all('img')
        total_images = len(images)
        images_without_alt = len([img for img in images if not img.get('alt')])

        return {
            'total': total_images,
            'without_alt': images_without_alt,
            'recommendations': [f"{images_without_alt}张图片缺少alt属性"] if images_without_alt > 0 else []
        }

    def _check_robots_txt(self):
        """检查robots.txt"""
        try:
            robots_url = urljoin(self.url, '/robots.txt')
            response = requests.get(robots_url, timeout=10)
            exists = response.status_code == 200
            return {
                'exists': exists,
                'recommendations': [] if exists else ['建议添加robots.txt文件']
            }
        except:
            return {
                'exists': False,
                'recommendations': ['无法访问robots.txt文件']
            }

    def _check_sitemap(self):
        """检查sitemap"""
        sitemap_urls = ['/sitemap.xml', '/sitemap_index.xml']
        for sitemap_url in sitemap_urls:
            try:
                full_url = urljoin(self.url, sitemap_url)
                response = requests.get(full_url, timeout=10)
                if response.status_code == 200:
                    return {
                        'exists': True,
                        'url': full_url,
                        'recommendations': []
                    }
            except:
                continue

        return {
            'exists': False,
            'recommendations': ['建议添加XML网站地图']
        }

    def _check_url_structure(self):
        """检查URL结构"""
        parsed_url = urlparse(self.url)
        path = parsed_url.path

        # 检查URL是否友好
        is_friendly = bool(re.match(r'^[a-zA-Z0-9\-/]*$', path))
        has_parameters = bool(parsed_url.query)

        recommendations = []

        if not is_friendly:
            recommendations.append("URL包含特殊字符，建议使用友好的URL结构")

        if has_parameters:
            recommendations.append("URL包含查询参数，可能影响SEO")

        return {
            'friendly': is_friendly,
            'has_parameters': has_parameters,
            'recommendations': recommendations
        }

    def _check_internal_links(self):
        """检查内部链接"""
        links = self.soup.find_all('a', href=True)
        internal_links = []

        for link in links:
            href = link['href']
            if href.startswith('/') or self.domain in href:
                internal_links.append(href)

        return {
            'count': len(internal_links),
            'recommendations': ['增加内部链接以改善网站结构'] if len(internal_links) < 10 else []
        }

    def _check_external_links(self):
        """检查外部链接"""
        links = self.soup.find_all('a', href=True)
        external_links = []

        for link in links:
            href = link['href']
            if href.startswith('http') and self.domain not in href:
                external_links.append(href)

        return {
            'count': len(external_links),
            'recommendations': []
        }

    def _get_content_recommendations(self, word_count):
        """获取内容建议"""
        recommendations = []
        if word_count < config.MIN_CONTENT_LENGTH:
            recommendations.append(f"内容太少，建议至少{config.MIN_CONTENT_LENGTH}个词")
        elif word_count < config.OPTIMAL_CONTENT_LENGTH:
            recommendations.append(f"建议增加内容到{config.OPTIMAL_CONTENT_LENGTH}个词以上")
        return recommendations

    def _check_duplicate_content(self):
        """检查重复内容（简单实现）"""
        # 这里只是一个简单的实现，实际应用中可能需要更复杂的算法
        return {
            'detected': False,
            'recommendations': []
        }

    def _get_performance_recommendations(self, load_time):
        """获取性能建议"""
        recommendations = []
        if load_time > 6:
            recommendations.append("页面加载时间过长，建议优化")
        elif load_time > 4:
            recommendations.append("页面加载时间较长，可以进一步优化")
        elif load_time > 2:
            recommendations.append("页面加载时间良好，可以微调优化")
        return recommendations

    def _get_size_recommendations(self, page_size):
        """获取大小建议"""
        recommendations = []
        if page_size > 2000:
            recommendations.append("页面过大，建议压缩图片和资源")
        elif page_size > 1000:
            recommendations.append("页面较大，建议优化资源")
        return recommendations

    def _check_responsive_design(self):
        """检查响应式设计"""
        # 简单检查CSS中是否有媒体查询
        style_tags = self.soup.find_all('style')
        link_tags = self.soup.find_all('link', rel='stylesheet')

        has_media_query = False

        # 检查内联样式
        for style in style_tags:
            if '@media' in style.get_text():
                has_media_query = True
                break

        return {
            'detected': has_media_query,
            'recommendations': [] if has_media_query else ['建议添加响应式设计支持']
        }

    def _generate_issues_summary(self):
        """生成问题总结"""
        issues = {
            'critical': [],    # 严重问题
            'important': [],   # 重要问题
            'minor': [],       # 轻微问题
            'suggestions': []  # 建议改进
        }

        # 检查基础SEO问题
        if 'basic_seo' in self.results:
            basic = self.results['basic_seo']

            # 标题问题
            if 'title' in basic:
                title_data = basic['title']
                if not title_data['content']:
                    issues['critical'].append({
                        'type': '基础SEO',
                        'item': '页面标题',
                        'issue': '页面缺少标题标签',
                        'impact': '严重影响搜索引擎理解页面内容',
                        'solution': '添加包含关键词的页面标题，长度控制在30-60字符'
                    })
                elif title_data['length'] < config.MIN_TITLE_LENGTH:
                    issues['important'].append({
                        'type': '基础SEO',
                        'item': '页面标题',
                        'issue': f'标题过短（{title_data["length"]}字符）',
                        'impact': '可能无法充分描述页面内容',
                        'solution': f'建议标题长度至少{config.MIN_TITLE_LENGTH}字符'
                    })
                elif title_data['length'] > config.MAX_TITLE_LENGTH:
                    issues['minor'].append({
                        'type': '基础SEO',
                        'item': '页面标题',
                        'issue': f'标题过长（{title_data["length"]}字符）',
                        'impact': '搜索结果中可能被截断',
                        'solution': f'建议标题长度不超过{config.MAX_TITLE_LENGTH}字符'
                    })

            # Meta描述问题
            if 'meta_description' in basic:
                desc_data = basic['meta_description']
                if not desc_data['content']:
                    issues['important'].append({
                        'type': '基础SEO',
                        'item': 'Meta描述',
                        'issue': '页面缺少Meta描述',
                        'impact': '搜索引擎无法获取页面摘要信息',
                        'solution': '添加120-160字符的页面描述'
                    })
                elif desc_data['length'] < config.MIN_DESCRIPTION_LENGTH:
                    issues['minor'].append({
                        'type': '基础SEO',
                        'item': 'Meta描述',
                        'issue': f'描述过短（{desc_data["length"]}字符）',
                        'impact': '可能无法充分描述页面内容',
                        'solution': f'建议描述长度至少{config.MIN_DESCRIPTION_LENGTH}字符'
                    })

            # H1标签问题
            if 'headings' in basic and 'h1' in basic['headings']:
                h1_data = basic['headings']['h1']
                if h1_data['count'] == 0:
                    issues['important'].append({
                        'type': '基础SEO',
                        'item': 'H1标签',
                        'issue': '页面缺少H1标签',
                        'impact': '影响页面结构和关键词权重',
                        'solution': '添加一个包含主要关键词的H1标签'
                    })
                elif h1_data['count'] > 1:
                    issues['minor'].append({
                        'type': '基础SEO',
                        'item': 'H1标签',
                        'issue': f'页面有{h1_data["count"]}个H1标签',
                        'impact': '可能分散关键词权重',
                        'solution': '每个页面应该只有一个H1标签'
                    })

            # 图片问题
            if 'images' in basic:
                img_data = basic['images']
                if img_data['without_alt'] > 0:
                    severity = 'important' if img_data['without_alt'] > 3 else 'minor'
                    issues[severity].append({
                        'type': '基础SEO',
                        'item': '图片优化',
                        'issue': f'{img_data["without_alt"]}张图片缺少alt属性',
                        'impact': '影响可访问性和图片搜索排名',
                        'solution': '为所有图片添加描述性的alt属性'
                    })

        # 检查技术SEO问题
        if 'technical_seo' in self.results:
            tech = self.results['technical_seo']

            # Robots.txt问题
            if 'robots_txt' in tech and not tech['robots_txt']['exists']:
                issues['suggestions'].append({
                    'type': '技术SEO',
                    'item': 'Robots.txt',
                    'issue': '网站缺少robots.txt文件',
                    'impact': '无法指导搜索引擎爬虫行为',
                    'solution': '创建robots.txt文件指导搜索引擎抓取'
                })

            # Sitemap问题
            if 'sitemap' in tech and not tech['sitemap']['exists']:
                issues['suggestions'].append({
                    'type': '技术SEO',
                    'item': 'XML网站地图',
                    'issue': '网站缺少XML网站地图',
                    'impact': '搜索引擎难以发现所有页面',
                    'solution': '创建并提交XML网站地图到搜索引擎'
                })

            # 内部链接问题
            if 'internal_links' in tech:
                link_count = tech['internal_links']['count']
                if link_count < 3:
                    issues['important'].append({
                        'type': '技术SEO',
                        'item': '内部链接',
                        'issue': f'内部链接过少（{link_count}个）',
                        'impact': '影响网站结构和页面权重传递',
                        'solution': '增加相关页面的内部链接'
                    })
                elif link_count < 10:
                    issues['minor'].append({
                        'type': '技术SEO',
                        'item': '内部链接',
                        'issue': f'内部链接较少（{link_count}个）',
                        'impact': '可以进一步改善网站结构',
                        'solution': '适当增加内部链接以改善用户体验'
                    })

        # 检查性能问题
        if 'performance' in self.results:
            perf = self.results['performance']

            # 加载时间问题
            if 'load_time' in perf:
                load_time = perf['load_time']['time']
                if load_time > 6:
                    issues['critical'].append({
                        'type': '性能优化',
                        'item': '页面加载时间',
                        'issue': f'加载时间过长（{load_time}秒）',
                        'impact': '严重影响用户体验和搜索排名',
                        'solution': '优化图片、压缩资源、使用CDN'
                    })
                elif load_time > 4:
                    issues['important'].append({
                        'type': '性能优化',
                        'item': '页面加载时间',
                        'issue': f'加载时间较长（{load_time}秒）',
                        'impact': '影响用户体验',
                        'solution': '优化页面资源和服务器响应时间'
                    })
                elif load_time > 2:
                    issues['suggestions'].append({
                        'type': '性能优化',
                        'item': '页面加载时间',
                        'issue': f'加载时间可以优化（{load_time}秒）',
                        'impact': '进一步提升用户体验',
                        'solution': '考虑进一步优化页面性能'
                    })

            # 页面大小问题
            if 'page_size' in perf:
                page_size = perf['page_size']['size']
                if page_size > 2000:
                    issues['important'].append({
                        'type': '性能优化',
                        'item': '页面大小',
                        'issue': f'页面过大（{page_size:.1f}KB）',
                        'impact': '影响加载速度，特别是移动端用户',
                        'solution': '压缩图片和资源文件'
                    })
                elif page_size > 1000:
                    issues['minor'].append({
                        'type': '性能优化',
                        'item': '页面大小',
                        'issue': f'页面较大（{page_size:.1f}KB）',
                        'impact': '可能影响移动端加载速度',
                        'solution': '考虑优化图片和资源大小'
                    })

        # 检查安全问题
        if 'security' in self.results:
            security = self.results['security']

            # HTTPS问题
            if 'https' in security and not security['https']['enabled']:
                issues['critical'].append({
                    'type': '安全性',
                    'item': 'HTTPS加密',
                    'issue': '网站未启用HTTPS',
                    'impact': '数据传输不安全，影响搜索排名',
                    'solution': '安装SSL证书，启用HTTPS加密'
                })

        # 检查移动端问题
        if 'mobile' in self.results:
            mobile = self.results['mobile']

            # Viewport问题
            if 'viewport' in mobile and not mobile['viewport']['exists']:
                issues['important'].append({
                    'type': '移动端优化',
                    'item': 'Viewport设置',
                    'issue': '缺少viewport meta标签',
                    'impact': '移动端显示效果差',
                    'solution': '添加viewport meta标签支持移动设备'
                })

            # 响应式设计问题
            if 'responsive' in mobile and not mobile['responsive']['detected']:
                issues['suggestions'].append({
                    'type': '移动端优化',
                    'item': '响应式设计',
                    'issue': '未检测到响应式设计',
                    'impact': '可能影响移动端用户体验',
                    'solution': '考虑实现响应式设计以适配不同设备'
                })

        # 检查内容问题
        if 'content_analysis' in self.results:
            content = self.results['content_analysis']

            # 内容长度问题
            if 'word_count' in content:
                word_count = content['word_count']['count']
                if word_count < config.MIN_CONTENT_LENGTH:
                    issues['important'].append({
                        'type': '内容质量',
                        'item': '内容长度',
                        'issue': f'内容过少（{word_count}词）',
                        'impact': '可能被搜索引擎认为内容质量低',
                        'solution': f'增加有价值的内容，建议至少{config.MIN_CONTENT_LENGTH}词'
                    })
                elif word_count < config.OPTIMAL_CONTENT_LENGTH:
                    issues['suggestions'].append({
                        'type': '内容质量',
                        'item': '内容长度',
                        'issue': f'内容可以更丰富（{word_count}词）',
                        'impact': '更多内容有助于提升搜索排名',
                        'solution': f'考虑增加内容到{config.OPTIMAL_CONTENT_LENGTH}词以上'
                    })

        # 统计问题数量
        total_issues = len(issues['critical']) + len(issues['important']) + len(issues['minor']) + len(issues['suggestions'])

        self.results['issues_summary'] = {
            'total_issues': total_issues,
            'critical_count': len(issues['critical']),
            'important_count': len(issues['important']),
            'minor_count': len(issues['minor']),
            'suggestions_count': len(issues['suggestions']),
            'issues': issues,
            'status': self._get_overall_status(issues)
        }

    def _get_overall_status(self, issues):
        """根据问题情况获取整体状态"""
        critical_count = len(issues['critical'])
        important_count = len(issues['important'])

        if critical_count > 0:
            return {
                'level': 'critical',
                'message': f'发现{critical_count}个严重问题，需要立即处理',
                'icon': '🚨'
            }
        elif important_count > 3:
            return {
                'level': 'warning',
                'message': f'发现{important_count}个重要问题，建议优先处理',
                'icon': '⚠️'
            }
        elif important_count > 0:
            return {
                'level': 'attention',
                'message': f'发现{important_count}个重要问题，建议处理',
                'icon': '🔧'
            }
        else:
            return {
                'level': 'good',
                'message': 'SEO状况良好，可以进行细节优化',
                'icon': '✅'
            }
