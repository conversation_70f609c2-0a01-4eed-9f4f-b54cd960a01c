
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .url {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .issues-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .status-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .issues-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .issue-stat {
            text-align: center;
            margin: 10px;
        }

        .issue-count {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .critical-count { color: #F44336; }
        .important-count { color: #FF9800; }
        .minor-count { color: #FFC107; }
        .suggestions-count { color: #2196F3; }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }

        .section-content {
            padding: 20px;
        }

        .check-item {
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }

        .check-item:last-child {
            border-bottom: none;
        }

        .check-name {
            font-weight: 500;
        }

        .issue-list {
            margin-top: 30px;
        }

        .issue-category {
            margin-bottom: 25px;
        }

        .issue-category h3 {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
        }

        .critical-issues h3 { background: #F44336; }
        .important-issues h3 { background: #FF9800; }
        .minor-issues h3 { background: #FFC107; color: #333; }
        .suggestions h3 { background: #2196F3; }

        .issue-item {
            background: #f8f9fa;
            border-left: 4px solid #dee2e6;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 5px 5px 0;
        }

        .critical-issues .issue-item { border-left-color: #F44336; }
        .important-issues .issue-item { border-left-color: #FF9800; }
        .minor-issues .issue-item { border-left-color: #FFC107; }
        .suggestions .issue-item { border-left-color: #2196F3; }

        .issue-header {
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
        }

        .issue-impact {
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 8px;
        }

        .issue-solution {
            color: #28a745;
            font-weight: 500;
        }

        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }

        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendations li:before {
            content: "⚠";
            position: absolute;
            left: 0;
            color: #856404;
        }

        .details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .score-circle {
                width: 120px;
                height: 120px;
                font-size: 2em;
            }

            .check-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .check-score {
                margin-left: 0;
                margin-top: 10px;
            }
        }
        