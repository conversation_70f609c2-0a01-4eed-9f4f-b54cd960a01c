#!/usr/bin/env python3
"""
SEO自动化检查工具
作者: 测试工程师
功能: 对网站进行全面的SEO检查并生成HTML报告
"""

import sys
import argparse
import validators
from seo_checker import SEOChecker
from report_generator import ReportGenerator

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SEO自动化检查工具')
    parser.add_argument('url', help='要检查的网站URL')
    parser.add_argument('-o', '--output', default='seo_report.html',
                       help='输出报告文件名 (默认: seo_report.html)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='显示详细输出')

    args = parser.parse_args()

    # 验证URL
    if not validators.url(args.url):
        print(f"错误: 无效的URL格式: {args.url}")
        sys.exit(1)

    print("=" * 60)
    print("🔍 SEO自动化检查工具")
    print("=" * 60)
    print(f"检查网站: {args.url}")
    print(f"输出文件: {args.output}")
    print("-" * 60)

    try:
        # 创建SEO检查器
        checker = SEOChecker(args.url)

        # 执行检查
        print("正在执行SEO检查...")
        results = checker.check_all()

        if results is None:
            print("❌ 检查失败: 无法获取网站内容")
            sys.exit(1)

        # 显示简要结果
        if 'issues_summary' in results:
            summary = results['issues_summary']
            print(f"✅ 检查完成!")
            print(f"{summary['status']['icon']} {summary['status']['message']}")
            print(f"📋 发现问题: 严重{summary['critical_count']}个, 重要{summary['important_count']}个, 轻微{summary['minor_count']}个, 建议{summary['suggestions_count']}个")

        # 生成报告
        print("正在生成HTML报告...")
        generator = ReportGenerator()
        output_file = generator.generate_html_report(results, args.url, args.output)

        print("=" * 60)
        print("🎉 SEO检查完成!")
        print(f"📄 报告已保存到: {output_file}")
        print("💡 请在浏览器中打开报告文件查看详细结果")
        print("=" * 60)

        # 显示详细结果（如果启用verbose模式）
        if args.verbose:
            print_detailed_results(results)

    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

def print_detailed_results(results):
    """打印详细结果"""
    print("\n📋 详细检查结果:")
    print("-" * 40)

    # 显示问题总结
    if 'issues_summary' in results:
        summary = results['issues_summary']
        print(f"\n{summary['status']['icon']} 总体状况: {summary['status']['message']}")
        print(f"📊 问题统计:")
        print(f"  🚨 严重问题: {summary['critical_count']}个")
        print(f"  ⚠️  重要问题: {summary['important_count']}个")
        print(f"  🔧 轻微问题: {summary['minor_count']}个")
        print(f"  💡 优化建议: {summary['suggestions_count']}个")

        # 显示前几个重要问题
        if summary['issues']['critical']:
            print(f"\n🚨 严重问题:")
            for issue in summary['issues']['critical'][:3]:
                print(f"  • {issue['item']}: {issue['issue']}")

        if summary['issues']['important']:
            print(f"\n⚠️  重要问题:")
            for issue in summary['issues']['important'][:3]:
                print(f"  • {issue['item']}: {issue['issue']}")

    # 基础SEO
    if 'basic_seo' in results:
        print("\n🔍 基础SEO:")
        basic = results['basic_seo']

        if 'title' in basic:
            title_data = basic['title']
            status = "✅" if not title_data['recommendations'] else "⚠️"
            print(f"  {status} 标题: {title_data['content'][:50]}...")
            print(f"    长度: {title_data['length']} 字符")

        if 'meta_description' in basic:
            desc_data = basic['meta_description']
            status = "✅" if not desc_data['recommendations'] else "⚠️"
            print(f"  {status} 描述: 长度 {desc_data['length']} 字符")

        if 'headings' in basic and 'h1' in basic['headings']:
            h1_data = basic['headings']['h1']
            status = "✅" if not h1_data['recommendations'] else "⚠️"
            print(f"  {status} H1标签: {h1_data['count']}个")

        if 'images' in basic:
            img_data = basic['images']
            status = "✅" if not img_data['recommendations'] else "⚠️"
            print(f"  {status} 图片: 总数{img_data['total']}, 缺少alt: {img_data['without_alt']}")

    # 技术SEO
    if 'technical_seo' in results:
        print("\n⚙️ 技术SEO:")
        tech = results['technical_seo']

        if 'robots_txt' in tech:
            robots_data = tech['robots_txt']
            status = "✅" if robots_data['exists'] else "⚠️"
            print(f"  {status} Robots.txt: {'存在' if robots_data['exists'] else '不存在'}")

        if 'sitemap' in tech:
            sitemap_data = tech['sitemap']
            status = "✅" if sitemap_data['exists'] else "⚠️"
            print(f"  {status} 网站地图: {'存在' if sitemap_data['exists'] else '不存在'}")

        if 'internal_links' in tech:
            links_data = tech['internal_links']
            status = "✅" if links_data['count'] >= 10 else "⚠️"
            print(f"  {status} 内部链接: {links_data['count']}个")

    # 性能
    if 'performance' in results:
        print("\n🚀 性能:")
        perf = results['performance']

        if 'load_time' in perf:
            load_data = perf['load_time']
            load_time = load_data['time']
            status = "✅" if load_time <= 3 else "⚠️"
            print(f"  {status} 加载时间: {load_time} 秒")

        if 'page_size' in perf:
            size_data = perf['page_size']
            page_size = size_data['size']
            status = "✅" if page_size <= 1000 else "⚠️"
            print(f"  {status} 页面大小: {page_size} KB")

    # 移动端
    if 'mobile' in results:
        print("\n📱 移动端:")
        mobile = results['mobile']

        if 'viewport' in mobile:
            viewport_data = mobile['viewport']
            status = "✅" if viewport_data['exists'] else "⚠️"
            print(f"  {status} Viewport: {'已设置' if viewport_data['exists'] else '未设置'}")

    # 安全
    if 'security' in results:
        print("\n🔒 安全:")
        security = results['security']

        if 'https' in security:
            https_data = security['https']
            status = "✅" if https_data['enabled'] else "🚨"
            print(f"  {status} HTTPS: {'已启用' if https_data['enabled'] else '未启用'}")

    # 内容分析
    if 'content_analysis' in results:
        print("\n📝 内容分析:")
        content = results['content_analysis']

        if 'word_count' in content:
            word_data = content['word_count']
            word_count = word_data['count']
            status = "✅" if word_count >= 300 else "⚠️"
            print(f"  {status} 内容长度: {word_count}词")

if __name__ == "__main__":
    main()
